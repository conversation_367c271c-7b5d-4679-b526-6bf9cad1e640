import { fileURLToPath, URL } from 'node:url';

import vue from '@vitejs/plugin-vue';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import Components from 'unplugin-vue-components/vite';
import { defineConfig } from 'vite';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    Components({
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass', // 使用sass样式
        }),
      ],
    }),
  ],

  // 🔧 依赖预构建优化
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'axios',
      'dayjs',
      'vue-axios',
      'element-plus',
      '@element-plus/icons-vue',
      'dompurify',
    ],
    force: false, // 不强制重新构建，除非必要
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    host: true,
    port: 7799,
    hmr: {
      overlay: true,
    },
    watch: {
      // usePolling: true, // 在原生Windows环境下，默认的文件系统事件优于轮询，此项可移除
      interval: 100,
    },
    // 🚀 开发服务器优化
    warmup: {
      clientFiles: ['./src/main.js', './src/App.vue'],
    },
    proxy: {
      // 服务器监控系统API代理
      '/api': {
        target: 'http://127.0.0.1:7788',
        changeOrigin: true,
        secure: false,
      },
      // WebSocket代理
      '/ws': {
        target: 'ws://127.0.0.1:7788',
        changeOrigin: true,
        ws: true,
      },
      // 静态资源代理（如果需要）
      '/dashboard': {
        target: 'http://127.0.0.1:7788',
        changeOrigin: true,
      },
    },
  },
  build: {
    // 🚀 极速构建优化
    minify: 'esbuild', // 使用 esbuild，速度最快
    target: 'es2020', // 提高目标版本，减少转换
    cssCodeSplit: true, // 启用CSS分割，确保主题样式正确加载
    sourcemap: false, // 生产环境不生成 sourcemap
    chunkSizeWarningLimit: 5000, // 提高警告阈值
    reportCompressedSize: false, // 跳过压缩大小报告

    // 🔧 Rollup 优化选项
    rollupOptions: {
      output: {
        // 智能代码分割策略
        manualChunks: {
          // Vue 核心库
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          // UI 组件库
          'ui-vendor': ['element-plus', '@element-plus/icons-vue'],
          // 工具库
          'utils-vendor': ['axios', 'dayjs', 'dompurify'],
        },
        // 优化文件名
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: assetInfo => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (
            /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)
          ) {
            return `media/[name]-[hash].${ext}`;
          }
          if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(assetInfo.name)) {
            return `img/[name]-[hash].${ext}`;
          }
          if (ext === 'css') {
            return `css/[name]-[hash].${ext}`;
          }
          return `assets/[name]-[hash].${ext}`;
        },
      },
      // 外部依赖优化（如果使用CDN）
      // external: ['vue', 'vue-router'],
    },

    // 🎯 esbuild 优化选项
    esbuild: {
      drop: ['console', 'debugger'], // 移除 console 和 debugger
      legalComments: 'none', // 移除法律注释
    },
  },
});
