@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=I:\agent_new\vue\node_modules\.pnpm\@babel+parser@7.28.0\node_modules\@babel\parser\bin\node_modules;I:\agent_new\vue\node_modules\.pnpm\@babel+parser@7.28.0\node_modules\@babel\parser\node_modules;I:\agent_new\vue\node_modules\.pnpm\@babel+parser@7.28.0\node_modules\@babel\node_modules;I:\agent_new\vue\node_modules\.pnpm\@babel+parser@7.28.0\node_modules;I:\agent_new\vue\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=I:\agent_new\vue\node_modules\.pnpm\@babel+parser@7.28.0\node_modules\@babel\parser\bin\node_modules;I:\agent_new\vue\node_modules\.pnpm\@babel+parser@7.28.0\node_modules\@babel\parser\node_modules;I:\agent_new\vue\node_modules\.pnpm\@babel+parser@7.28.0\node_modules\@babel\node_modules;I:\agent_new\vue\node_modules\.pnpm\@babel+parser@7.28.0\node_modules;I:\agent_new\vue\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin\babel-parser.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin\babel-parser.js" %*
)
