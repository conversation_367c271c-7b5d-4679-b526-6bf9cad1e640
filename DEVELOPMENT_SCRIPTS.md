# 开发脚本使用说明

本项目提供了两个PowerShell脚本来简化开发流程：

## 1. hot_reload.ps1 - Go热加载开发脚本

### 功能特性
- 🔥 **自动热重载** - 代码修改后自动重新编译和运行
- 🛠️ **自动安装工具** - 自动检查并安装Air热重载工具
- ⚙️ **智能配置** - 自动创建和管理.air.toml配置文件
- 🎯 **多模式支持** - 支持服务端、客户端和默认模式
- 📝 **详细日志** - 彩色输出，清晰的状态提示

### 使用方法

```powershell
# 基本用法
.\hot_reload.ps1                    # 默认模式运行main.go

# 服务端模式
.\hot_reload.ps1 -Server            # 运行 main.go -s

# 客户端模式  
.\hot_reload.ps1 -Client            # 运行 main.go -c

# 使用自定义配置文件
.\hot_reload.ps1 -Config custom.toml

# 显示帮助
.\hot_reload.ps1 -Help
```

### 首次使用
1. 脚本会自动检查Go是否安装
2. 自动安装Air工具（如果未安装）
3. 创建.air.toml配置文件（如果不存在）
4. 启动热重载开发环境

### 配置文件说明
脚本会自动创建`.air.toml`配置文件，包含以下特性：
- 排除vue、web、dist等前端目录
- 监控.go文件变化
- 自动重新编译和运行
- 错误日志记录

---

## 2. test_three_client.ps1 - 三端同时运行脚本

### 功能特性
- 🚀 **一键启动** - 同时启动Go服务端、Go客户端和Vue前端
- 🔍 **端口检查** - 自动检查端口占用情况
- 📊 **实时监控** - 显示各服务的启动状态和日志
- ⚙️ **参数配置** - 支持自定义端口和启动延迟
- 🛡️ **错误处理** - 完善的错误处理和进程清理
- 📈 **状态统计** - 显示运行时长和服务状态

### 使用方法

```powershell
# 基本用法（使用默认端口）
.\test_three_client.ps1

# 自定义端口
.\test_three_client.ps1 -ServerPort 8080 -VuePort 3000

# 设置启动延迟
.\test_three_client.ps1 -Delay 5

# 跳过端口检查
.\test_three_client.ps1 -SkipPortCheck

# 显示帮助
.\test_three_client.ps1 -Help
```

### 默认配置
- **Go服务端端口**: 7788
- **Vue前端端口**: 7799  
- **启动延迟**: 3秒
- **自动端口检查**: 启用

### 启动流程
1. 检查必要工具（Go、pnpm）
2. 检查端口占用情况
3. 启动Go服务端（main.go -s）
4. 等待指定延迟时间
5. 启动Go客户端（main.go -c）
6. 启动Vue开发服务器（pnpm dev）
7. 显示服务访问地址

### 停止服务
- 按任意键或Ctrl+C停止所有服务
- 脚本会自动清理所有启动的进程
- 显示运行时长统计

---

## 开发工作流建议

### 单独开发Go后端
```powershell
# 使用热重载开发服务端
.\hot_reload.ps1 -Server

# 或者开发客户端
.\hot_reload.ps1 -Client
```

### 全栈开发
```powershell
# 同时启动所有服务进行全栈开发
.\test_three_client.ps1
```

### 前端开发
```powershell
# 进入vue目录单独开发前端
cd vue
pnpm dev
```

---

## 故障排除

### Air安装失败
```powershell
# 手动安装Air
go install github.com/air-verse/air@latest

# 设置代理（如果网络问题）
$env:GOPROXY = "https://goproxy.cn,direct"
go install github.com/air-verse/air@latest
```

### 端口被占用
```powershell
# 查看端口占用
netstat -ano | findstr :7788
netstat -ano | findstr :7799

# 杀死占用进程
taskkill /PID <进程ID> /F
```

### pnpm未安装
```powershell
# 安装pnpm
npm install -g pnpm

# 或使用Chocolatey
choco install pnpm
```

### Go未安装
1. 访问 https://golang.org/dl/
2. 下载并安装Go
3. 确保Go在PATH环境变量中

---

## 脚本特性对比

| 特性 | hot_reload.ps1 | test_three_client.ps1 |
|------|----------------|----------------------|
| Go热重载 | ✅ | ❌ |
| 多进程管理 | ❌ | ✅ |
| 前端开发 | ❌ | ✅ |
| 端口检查 | ❌ | ✅ |
| 自动安装工具 | ✅ | ❌ |
| 配置文件管理 | ✅ | ❌ |
| 实时日志 | ✅ | ✅ |

---

## 注意事项

1. **权限要求**: 脚本需要执行权限，可能需要设置PowerShell执行策略
2. **网络要求**: 首次使用需要网络连接下载Air工具
3. **端口冲突**: 确保默认端口7788和7799未被其他程序占用
4. **进程清理**: 脚本会自动清理启动的进程，但建议正常退出
5. **日志文件**: Air会生成build-errors.log文件记录构建错误

---

## 更新日志

### v1.0.0 (2025-01-01)
- ✅ 创建hot_reload.ps1脚本
- ✅ 重构test_three_client.ps1脚本
- ✅ 添加完整的错误处理
- ✅ 支持参数配置
- ✅ 添加端口检查功能
- ✅ 改进日志输出和用户体验
