# Simple PowerShell build script for Go project
# Build for Windows, Linux x64, and Linux ARM64

# Check if Go is installed
if (-not (Get-Command go -ErrorAction SilentlyContinue)) {
    Write-Host "Error: Go is not installed. Please install Go first." -ForegroundColor Red
    exit 1
}

# Create dist directory
$distDir = "dist"
if (-not (Test-Path $distDir)) {
    New-Item -ItemType Directory -Path $distDir | Out-Null
    Write-Host "Created dist directory."
}

# Build Windows x64 with CGO enabled (using MSYS2 GCC)
Write-Host "Building Windows x64 with CGO..."
$env:GOOS = "windows"
$env:GOARCH = "amd64"
$env:CGO_ENABLED = "1"
$env:CC = "C:\msys64\ucrt64\bin\gcc.exe"
$env:CXX = "C:\msys64\ucrt64\bin\g++.exe"
go build -o "dist/main_win_amd64_cgo.exe" main.go
if (Test-Path "dist/main_win_amd64_cgo.exe") {
    Write-Host "Success: Generated dist/main_win_amd64_cgo.exe with CGO" -ForegroundColor Green
} else {
    Write-Host "Error: Windows x64 CGO build failed." -ForegroundColor Red
}

# Build Linux x64
Write-Host "Building Linux x64..."
$env:GOOS = "linux"
$env:GOARCH = "amd64"
$env:CGO_ENABLED = "0"
go build -o "dist/main_linux_amd64" main.go
if (Test-Path "dist/main_linux_amd64") {
    Write-Host "Success: Generated dist/main_linux_amd64" -ForegroundColor Green
} else {
    Write-Host "Error: Linux x64 build failed." -ForegroundColor Red
}

# Build Linux ARM64
Write-Host "Building Linux ARM64..."
$env:GOOS = "linux"
$env:GOARCH = "arm64"
$env:CGO_ENABLED = "0"
go build -o "dist/main_linux_arm64" main.go
if (Test-Path "dist/main_linux_arm64") {
    Write-Host "Success: Generated dist/main_linux_arm64" -ForegroundColor Green
} else {
    Write-Host "Error: Linux ARM64 build failed." -ForegroundColor Red
}

Write-Host "Build process completed." -ForegroundColor Cyan

# List generated files
Write-Host "Generated files:"
Get-ChildItem -Path $distDir | ForEach-Object {
    $size = [math]::Round($_.Length / 1MB, 2)
    Write-Host "  $($_.Name) - ${size}MB"
}
