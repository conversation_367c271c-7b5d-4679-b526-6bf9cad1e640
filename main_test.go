package main

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
)

// TestHandleControlServiceAPI tests the service control endpoints
func TestHandleControlServiceAPI(t *testing.T) {
	tests := []struct {
		name           string
		method         string
		path           string
		body           ServiceRequest
		expectedStatus int
	}{
		{
			name:   "Valid supervisor start request",
			method: "POST",
			path:   "/api/services/supervisor/start",
			body: ServiceRequest{
				ServerID:    1,
				ServiceName: "test-service",
				ServiceType: "supervisor",
			},
			expectedStatus: http.StatusInternalServerError, // Will fail but that's expected
		},
		{
			name:   "Valid systemd stop request",
			method: "POST",
			path:   "/api/services/systemd/stop",
			body: ServiceRequest{
				ServerID:    1,
				ServiceName: "test-service",
				ServiceType: "systemd",
			},
			expectedStatus: http.StatusInternalServerError, // Will fail but that's expected
		},
		{
			name:   "Valid docker restart request",
			method: "POST",
			path:   "/api/services/docker/restart",
			body: ServiceRequest{
				ServerID:    1,
				ServiceName: "test-container",
				ServiceType: "docker",
			},
			expectedStatus: http.StatusInternalServerError, // Will fail but that's expected
		},
		{
			name:   "Missing serviceName",
			method: "POST",
			path:   "/api/services/supervisor/start",
			body: ServiceRequest{
				ServerID:    1,
				ServiceType: "supervisor",
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:   "Missing serviceType",
			method: "POST",
			path:   "/api/services/supervisor/start",
			body: ServiceRequest{
				ServerID:    1,
				ServiceName: "test-service",
			},
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			bodyBytes, err := json.Marshal(test.body)
			if err != nil {
				t.Fatalf("Failed to marshal request body: %v", err)
			}

			req, err := http.NewRequest(test.method, test.path, bytes.NewBuffer(bodyBytes))
			if err != nil {
				t.Fatalf("Failed to create request: %v", err)
			}
			req.Header.Set("Content-Type", "application/json")

			rr := httptest.NewRecorder()
			handler := http.HandlerFunc(handleControlServiceAPI)
			handler.ServeHTTP(rr, req)

			if rr.Code != test.expectedStatus {
				t.Errorf("Expected status %d, got %d. Response: %s", 
					test.expectedStatus, rr.Code, rr.Body.String())
			}
		})
	}
}

// TestEncryptDecrypt tests the encryption and decryption functions
func TestEncryptDecrypt(t *testing.T) {
	testData := []byte("This is test data for encryption")
	password := "test-password-123"

	// Test encryption
	encrypted, err := encrypt(testData, password)
	if err != nil {
		t.Fatalf("Encryption failed: %v", err)
	}

	if len(encrypted) == 0 {
		t.Fatal("Encrypted data is empty")
	}

	// Test decryption
	decrypted, err := decrypt(encrypted, password)
	if err != nil {
		t.Fatalf("Decryption failed: %v", err)
	}

	if string(decrypted) != string(testData) {
		t.Errorf("Decrypted data doesn't match original. Expected: %s, Got: %s", 
			string(testData), string(decrypted))
	}
}

// TestExecuteCommand tests the executeCommand function
func TestExecuteCommand(t *testing.T) {
	tests := []struct {
		name        string
		command     string
		args        []string
		expectError bool
	}{
		{
			name:        "Valid command - echo",
			command:     "echo",
			args:        []string{"hello"},
			expectError: false,
		},
		{
			name:        "Invalid command",
			command:     "nonexistentcommand12345",
			args:        []string{},
			expectError: true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			output, err := executeCommand(test.command, test.args...)
			
			if test.expectError && err == nil {
				t.Errorf("Expected error for command %s, but got none", test.command)
			}
			
			if !test.expectError && err != nil {
				t.Errorf("Unexpected error for command %s: %v", test.command, err)
			}
			
			if !test.expectError && len(output) == 0 {
				t.Errorf("Expected output for command %s, but got empty string", test.command)
			}
		})
	}
}
