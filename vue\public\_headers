# Content Security Policy for Server Monitor System
/*
  # 严格的内容安全策略，确保所有资源本地化
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self' ws: wss:; media-src 'self'; object-src 'none'; frame-src 'none'; base-uri 'self'; form-action 'self'
  
  # 防止点击劫持
  X-Frame-Options: DENY
  
  # 防止MIME类型嗅探
  X-Content-Type-Options: nosniff
  
  # XSS保护
  X-XSS-Protection: 1; mode=block
  
  # 引用者策略
  Referrer-Policy: strict-origin-when-cross-origin
  
  # 权限策略
  Permissions-Policy: geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()
  
  # 严格传输安全（如果使用HTTPS）
  # Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
