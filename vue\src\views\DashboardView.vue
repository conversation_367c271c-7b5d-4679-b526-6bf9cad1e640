<template>
  <div
    class="bg-gradient-to-br from-white via-gray-50 to-gray-100 min-h-screen"
  >
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <h1 class="text-xl font-bold text-gray-900">服务器监控系统</h1>
            </div>
            <div class="hidden md:ml-6 md:flex md:space-x-8">
              <router-link
                to="/dashboard"
                class="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >仪表板</router-link
              >
              <router-link
                to="/services"
                class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >服务管理</router-link
              >
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-500">{{ currentTime }}</span>
            <button
              @click="logout"
              class="text-gray-500 hover:text-red-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
              title="注销"
            >
              注销
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8 relative">
      <div v-if="isLoadingData" class="loading-overlay">
        <div class="spinner"></div>
        <p class="text-white mt-4">加载中...</p>
      </div>
      <div class="px-4 py-6 sm:px-0" :class="{ 'opacity-50': isLoadingData }">
        <!-- 页面标题 -->
        <div class="mb-8">
          <div
            class="flex flex-col md:flex-row md:items-center md:justify-between"
          >
            <div>
              <h2 class="text-3xl font-bold text-gray-900">服务器监控仪表板</h2>
              <p class="mt-2 text-gray-600">
                实时监控服务器的系统状态和性能指标
              </p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-wrap gap-3">
              <!-- 搜索框 -->
              <div class="relative">
                <input
                  type="text"
                  v-model="debouncedSearchQuery"
                  placeholder="搜索服务器..."
                  class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  :disabled="isLoadingData"
                />
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <svg
                    class="h-5 w-5 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    ></path>
                  </svg>
                </div>
              </div>
              <!-- 状态过滤 -->
              <select
                v-model="statusFilter"
                class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                :disabled="isLoadingData"
              >
                <option value="all">所有状态</option>
                <option value="online">在线</option>
                <option value="warning">警告</option>
                <option value="offline">离线</option>
              </select>
              <!-- 刷新按钮 -->
              <button
                @click="forceUpdate"
                class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                :disabled="isLoadingData"
              >
                <svg
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- 统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-green-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">在线服务器</p>
                <p class="text-2xl font-bold text-gray-900">
                  {{ onlineCount }}
                </p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-blue-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">总服务器</p>
                <p class="text-2xl font-bold text-gray-900">
                  {{ totalCount }}
                </p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-yellow-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">警告</p>
                <p class="text-2xl font-bold text-gray-900">
                  {{ warningCount }}
                </p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-purple-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">平均CPU</p>
                <p class="text-2xl font-bold text-gray-900">
                  {{ avgCpu.toFixed(0) }}%
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 网络流量统计行 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-green-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">上传流量</p>
                <p class="text-2xl font-bold text-gray-900">
                  {{ formatBytes(totalUploadTraffic) }}
                </p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-blue-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">下载流量</p>
                <p class="text-2xl font-bold text-gray-900">
                  {{ formatBytes(totalDownloadTraffic) }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 服务器网格 -->
        <div class="server-grid">
          <div
            v-if="filteredServers.length === 0"
            class="col-span-full text-center py-12"
          >
            <svg
              class="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33"
              ></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">
              没有找到服务器
            </h3>
            <p class="mt-1 text-sm text-gray-500">请尝试调整搜索条件或过滤器</p>
          </div>
          <div
            v-for="server in filteredServers"
            :key="server.id"
            class="server-card compact-card bg-white rounded-lg shadow-sm p-4 cursor-pointer hover:shadow-md"
            @click="navigateToDetail(server.id, server.name)"
          >
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center">
                <span
                  :class="['status-indicator', getStatusClass(server.status)]"
                ></span>
                <h3 class="text-sm font-semibold text-gray-900 truncate">
                  {{ server.name }}
                </h3>
              </div>
              <span
                :class="[
                  'text-xs font-medium',
                  getStatusTextClass(server.status),
                ]"
                >{{ getStatusText(server.status) }}</span
              >
            </div>

            <div class="mb-3">
              <div class="text-xs text-gray-600 truncate">
                {{ server.hostname }}
              </div>
              <div class="text-xs text-gray-500">{{ server.ip }}</div>
            </div>

            <div class="mb-3">
              <div class="flex justify-between items-center mb-1">
                <span class="text-xs font-medium text-gray-700">CPU</span>
                <span class="text-xs font-bold text-gray-900"
                  >{{ server.cpu.toFixed(1) }}%</span
                >
              </div>
              <el-progress
                :percentage="server.cpu"
                :color="getProgressBarColor(server.cpu, 'cpu')"
                :format="percentage => percentage.toFixed(1) + '%'"
                :text-inside="true"
                :stroke-width="24"
                status="success"
              ></el-progress>
            </div>

            <div class="mb-3">
              <div class="flex justify-between items-center mb-1">
                <span class="text-xs font-medium text-gray-700">内存</span>
                <span class="text-xs font-bold text-gray-900"
                  >{{ server.memory.toFixed(1) }}%</span
                >
              </div>
              <el-progress
                :percentage="server.memory"
                :color="getProgressBarColor(server.memory, 'memory')"
                :format="percentage => percentage.toFixed(1) + '%'"
                :text-inside="true"
                :stroke-width="24"
                status="success"
              ></el-progress>
            </div>

            <div
              class="mb-3 bg-gradient-to-r from-blue-50 to-indigo-50 p-2 rounded-lg border border-blue-100"
            >
              <div class="grid grid-cols-2 gap-2 text-xs">
                <div class="flex items-center space-x-1">
                  <svg
                    class="w-3 h-3 text-green-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span class="text-gray-600">下载</span>
                  <span class="font-semibold text-green-600">{{
                    formatNetworkSpeed(server.NetInSpeed)
                  }}</span>
                </div>
                <div class="flex items-center space-x-1">
                  <svg
                    class="w-3 h-3 text-blue-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 11-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span class="text-gray-600">上传</span>
                  <span class="font-semibold text-blue-600">{{
                    formatNetworkSpeed(server.NetOutSpeed)
                  }}</span>
                </div>
              </div>
            </div>

            <div class="text-xs text-gray-500 text-center">
              运行时间: {{ server.uptime }}
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ElProgress, ElMessage } from 'element-plus'; // Import ElProgress and ElMessage components
import { computed, onMounted, onUnmounted, ref, getCurrentInstance } from 'vue';
import { useRouter } from 'vue-router';
import { useWebSocket } from '../composables/useWebSocket';
import { useServersStore } from '../store/servers';
import { formatBytes, formatUptime } from '../utils/formatters';
import { APP_CONFIG } from '../config/appConfig';
import { debounce } from '../utils/debounce';

const router = useRouter();
const serversStore = useServersStore();
const searchQuery = ref('');
const debouncedSearchQuery = ref(''); // New ref for debounced search input
const statusFilter = ref('all');
const currentTime = ref('');
const totalUploadTraffic = ref(0);
const totalDownloadTraffic = ref(0);
const isLoadingSystemStats = ref(false); // New ref for system stats loading

let dataFetchInterval = null;
let timeUpdateInterval = null;

// Computed properties for statistics
const onlineCount = computed(() => serversStore.onlineServers.length);
const totalCount = computed(() => serversStore.totalServers);
const warningCount = computed(() => serversStore.warningServers.length);
const avgCpu = computed(() => serversStore.averageCpu);

// Combined loading state
const isLoadingData = computed(
  () => serversStore.loading || isLoadingSystemStats.value
);

// Filtered servers for display
// Debounce the search query update
const debouncedUpdateSearchQuery = debounce(value => {
  searchQuery.value = value;
}, 300);

watch(debouncedSearchQuery, newValue => {
  debouncedUpdateSearchQuery(newValue);
});

const filteredServers = computed(() => {
  return serversStore.searchServers(searchQuery.value, statusFilter.value);
});

// Methods
const getStatusClass = status => {
  return {
    'status-online': status === 'online',
    'status-offline': status === 'offline',
    'status-warning': status === 'warning',
  };
};

const getStatusText = status => {
  switch (status) {
    case 'online':
      return '在线';
    case 'offline':
      return '离线';
    case 'warning':
      return '警告';
    default:
      return '未知';
  }
};

const getStatusTextClass = status => {
  return {
    'text-green-600': status === 'online',
    'text-red-600': status === 'offline',
    'text-yellow-600': status === 'warning',
  };
};

const getProgressBarColor = (percentage, type) => {
  if (type === 'cpu') {
    if (percentage > APP_CONFIG.CPU_WARNING_THRESHOLD) return '#f56c6c'; // Red
    if (percentage > APP_CONFIG.CPU_ORANGE_THRESHOLD) return '#e6a23c'; // Orange
    return '#67c23a'; // Green
  } else if (type === 'memory') {
    if (percentage > APP_CONFIG.MEMORY_WARNING_THRESHOLD) return '#f56c6c'; // Red
    if (percentage > APP_CONFIG.MEMORY_ORANGE_THRESHOLD) return '#e6a23c'; // Orange
    return '#409eff'; // Blue
  }
  return '#67c23a'; // Default green
};

// formatBytes is now imported from formatters.js

const formatNetworkSpeed = bytesPerSecond => {
  if (!bytesPerSecond || bytesPerSecond === 0) return '0 B/s';
  const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
  let size = bytesPerSecond;
  let unitIndex = 0;
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  const decimals = size >= 100 ? 0 : size >= 10 ? 1 : 2;
  return `${size.toFixed(decimals)} ${units[unitIndex]}`;
};

// formatUptime is now imported from formatters.js

const logout = () => {
  // In a real application, you would also invalidate the session on the server
  router.push('/login');
};

const navigateToDetail = (serverId, serverName) => {
  router.push({
    path: '/services',
    query: { mid: serverId, server: serverName },
  });
};

const forceUpdate = () => {
  fetchRealSystemData();
};

// Data fetching and WebSocket logic
const fetchRealSystemData = async () => {
  isLoadingSystemStats.value = true; // Set loading to true
  try {
    const response = await proxy.$axios.get('/api/system/stats', {
      withCredentials: true,
    });
    totalUploadTraffic.value = response.data.NetOutTransfer || 0;
    totalDownloadTraffic.value = response.data.NetInTransfer || 0;
    // For debugging/testing purposes, you might want to log the raw data
    // console.log("Raw system stats:", response.data);
  } catch (error) {
    console.error('Failed to fetch real system data:', error);
    ElMessage.error('获取实时系统数据失败: ' + error.message);
  } finally {
    isLoadingSystemStats.value = false; // Set loading to false
  }
};

const startHttpPolling = () => {
  console.log('Starting HTTP polling fallback');
  // Use serversStore.fetchServers to get updated server data
  // Also fetch overall system stats
  httpPollingInterval = setInterval(async () => {
    await serversStore.fetchServers();
    fetchRealSystemData();
  }, APP_CONFIG.SYSTEM_STATS_HTTP_POLLING_INTERVAL);
};

const {
  ws,
  isConnected,
  reconnectAttempts,
  error,
  connect,
  disconnect,
  sendMessage,
} = useWebSocket(
  `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/frontend`,
  {
    onOpen: () => {
      console.log('WebSocket connected');
      // Authenticate and subscribe to all servers
      sendMessage(
        JSON.stringify({
          type: 'auth',
          timestamp: new Date().toISOString(),
        })
      );
    },
    onMessage: event => {
      try {
        const message = JSON.parse(event.data);
        handleWebSocketMessage(message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    },
    onReconnectFail: () => {
      console.log(
        'Max reconnection attempts reached for WebSocket, falling back to HTTP polling'
      );
      startHttpPolling();
    },
    onError: err => {
      console.error('WebSocket error:', err);
      // No explicit action needed here, onclose will handle reconnection/fallback
    },
  }
);

const handleWebSocketMessage = message => {
  switch (message.type) {
    case 'auth_response':
      if (message.error) {
        console.error('WebSocket auth failed:', message.error);
        ElMessage.error('WebSocket 认证失败: ' + message.error);
        startHttpPolling();
      } else {
        console.log('WebSocket authenticated:', message.data);
        // Subscribe to all servers after successful authentication
        // Subscribe to all available servers after successful authentication
        serversStore.servers.forEach(server => {
          sendMessage(
            JSON.stringify({
              type: 'subscribe',
              server_id: server.id,
              timestamp: new Date().toISOString(),
            })
          );
        });
      }
      break;
    case 'subscribe_response':
      console.log('Subscribed to server:', message.server_id);
      break;
    case 'system_stats_broadcast':
      serversStore.updateServerStats(message.server_id, message.data);
      break;
    case 'pong':
      break;
    default:
      console.log('Unknown WebSocket message type:', message.type);
  }
};

const updateTime = () => {
  currentTime.value = new Date().toLocaleString();
};

// Lifecycle hooks
onMounted(async () => {
  console.log('Dashboard component mounted');
  serversStore.initializeServers();

  // Initial data fetch
  const isAuthenticated = await serversStore.fetchServers();
  if (!isAuthenticated) {
    return;
  }
  fetchRealSystemData(); // Fetch overall traffic stats

  // Set up intervals
  dataFetchInterval = setInterval(async () => {
    await serversStore.fetchServers();
    fetchRealSystemData();
  }, APP_CONFIG.DASHBOARD_DATA_FETCH_INTERVAL); // Every 30 seconds for server list and overall traffic

  timeUpdateInterval = setInterval(updateTime, APP_CONFIG.TIME_UPDATE_INTERVAL); // Every second for current time

  // Initialize WebSocket for real-time stats
  connect();
});

onUnmounted(() => {
  console.log('Dashboard component unmounted');
  if (dataFetchInterval) clearInterval(dataFetchInterval);
  if (timeUpdateInterval) clearInterval(timeUpdateInterval);
  if (httpPollingInterval) clearInterval(httpPollingInterval); // Clear http polling interval if it was set
  disconnect(); // Disconnect WebSocket
});
</script>

<style scoped>
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: 0.5rem; /* Match main content area's rounded corners */
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #fff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.server-card {
  border: 1px solid #e5e7eb;
}

.server-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 6px;
}

.status-online {
  background-color: #10b981;
}

.status-offline {
  background-color: #ef4444;
}

.status-warning {
  background-color: #f59e0b;
}

.progress-bar {
  height: 6px;
  border-radius: 3px;
}

.server-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

@media (min-width: 1024px) {
  .server-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1280px) {
  .server-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

.compact-card {
  min-height: 200px;
}
</style>
