import { onUnmounted, ref } from 'vue';

export function useWebSocket(url, options = {}) {
  const {
    maxReconnectAttempts = 5,
    reconnectInterval = 3000,
    onMessage: externalOnMessage,
    onOpen: externalOnOpen,
    onClose: externalOnClose,
    onError: externalOnError,
    onReconnectFail: externalOnReconnectFail,
  } = options;

  const ws = ref(null);
  const isConnected = ref(false);
  const reconnectAttempts = ref(0);
  const error = ref(null);
  let reconnectTimer = null;

  const connect = () => {
    error.value = null;
    ws.value = new WebSocket(url);

    ws.value.onopen = () => {
      console.log('WebSocket connected');
      isConnected.value = true;
      reconnectAttempts.value = 0;
      clearTimeout(reconnectTimer);
      if (externalOnOpen) externalOnOpen();
    };

    ws.value.onmessage = event => {
      if (externalOnMessage) externalOnMessage(event);
    };

    ws.value.onclose = () => {
      console.log('WebSocket disconnected');
      isConnected.value = false;
      if (reconnectAttempts.value < maxReconnectAttempts) {
        reconnectAttempts.value++;
        console.log(
          `Attempting to reconnect (${reconnectAttempts.value}/${maxReconnectAttempts})...`
        );
        reconnectTimer = setTimeout(connect, reconnectInterval);
      } else {
        console.log('Max reconnection attempts reached.');
        if (externalOnReconnectFail) externalOnReconnectFail();
      }
      if (externalOnClose) externalOnClose();
    };

    ws.value.onerror = err => {
      console.error('WebSocket error:', err);
      error.value = err;
      isConnected.value = false;
      if (externalOnError) externalOnError(err);
    };
  };

  const disconnect = () => {
    if (ws.value) {
      ws.value.close();
      ws.value = null;
    }
    isConnected.value = false;
    clearTimeout(reconnectTimer);
  };

  const sendMessage = message => {
    if (ws.value && isConnected.value) {
      ws.value.send(message);
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message);
    }
  };

  onUnmounted(() => {
    disconnect();
  });

  return {
    ws,
    isConnected,
    reconnectAttempts,
    error,
    connect,
    disconnect,
    sendMessage,
  };
}
