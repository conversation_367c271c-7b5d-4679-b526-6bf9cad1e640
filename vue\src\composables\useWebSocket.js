import { onUnmounted, ref } from 'vue';

export function useWebSocket(url, options = {}) {
  const {
    maxReconnectAttempts = 5,
    reconnectInterval = 3000,
    onMessage: externalOnMessage, // Deprecated, use messageHandlers
    onOpen: externalOnOpen,
    onClose: externalOnClose,
    onError: externalOnError,
    onReconnectFail: externalOnReconnectFail,
    messageHandlers = {}, // New option for specific message type handlers
  } = options;

  const ws = ref(null);
  const isConnected = ref(false);
  const reconnectAttempts = ref(0);
  const error = ref(null);
  let reconnectTimer = null;

  const connect = () => {
    error.value = null;
    ws.value = new WebSocket(url);

    ws.value.onopen = () => {
      console.log('WebSocket connected');
      isConnected.value = true;
      reconnectAttempts.value = 0;
      clearTimeout(reconnectTimer);
      if (externalOnOpen) externalOnOpen();
    };

    ws.value.onmessage = event => {
      try {
        const message = JSON.parse(event.data);

        // Handle error messages from the backend
        if (message.type === 'error' && message.error) {
          console.error('WebSocket backend error:', message.error);
          error.value = message.error; // Update local error state
          if (externalOnError) externalOnError(message.error); // Call external error handler
          return; // Stop further processing for error messages
        }

        // Handle ping-pong for connection keep-alive
        if (message.type === 'ping') {
          sendMessage({ type: 'pong' }); // Respond with pong
          return; // Stop further processing for ping messages
        }

        // Pass the parsed message to the external handler
        // Call specific handler if defined, otherwise call generic onMessage
        if (messageHandlers[message.type]) {
          messageHandlers[message.type](message);
        } else if (externalOnMessage) {
          externalOnMessage(message);
        }
      } catch (e) {
        console.error(
          'WebSocket message parsing error:',
          e,
          'Raw data:',
          event.data
        );
        error.value = 'Failed to parse WebSocket message: ' + e.message;
        if (externalOnError) externalOnError(e); // Call external error handler
      }
    };

    ws.value.onclose = () => {
      console.log('WebSocket disconnected');
      isConnected.value = false;
      if (reconnectAttempts.value < maxReconnectAttempts) {
        reconnectAttempts.value++;
        console.log(
          `Attempting to reconnect (${reconnectAttempts.value}/${maxReconnectAttempts})...`
        );
        reconnectTimer = setTimeout(connect, reconnectInterval);
      } else {
        console.log('Max reconnection attempts reached.');
        if (externalOnReconnectFail) externalOnReconnectFail();
      }
      if (externalOnClose) externalOnClose();
    };

    ws.value.onerror = err => {
      console.error('WebSocket error:', err);
      error.value = err;
      isConnected.value = false;
      if (externalOnError) externalOnError(err);
    };
  };

  const disconnect = () => {
    if (ws.value) {
      ws.value.close();
      ws.value = null;
    }
    isConnected.value = false;
    clearTimeout(reconnectTimer);
  };

  const sendMessage = message => {
    if (ws.value && isConnected.value) {
      ws.value.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message);
    }
  };

  onUnmounted(() => {
    disconnect();
  });

  return {
    ws,
    isConnected,
    reconnectAttempts,
    error,
    connect,
    disconnect,
    sendMessage, // Renamed to sendMessage as it now sends JSON
  };
}
