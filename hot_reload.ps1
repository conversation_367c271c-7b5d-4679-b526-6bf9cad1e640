# hot_reload.ps1 - Go开发热加载专用脚本
# 使用Air工具实现Go应用的热重载开发

param(
    [string]$Mode = "",
    [string]$Config = ".air.toml",
    [switch]$Server,
    [switch]$Client,
    [switch]$Help
)

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 显示帮助信息
function Show-Help {
    Write-ColorOutput "Go热加载开发脚本 - 使用Air工具" "Cyan"
    Write-ColorOutput "用法:" "Yellow"
    Write-ColorOutput "  .\hot_reload.ps1                    # 默认模式，运行main.go" "White"
    Write-ColorOutput "  .\hot_reload.ps1 -Server            # 服务端模式 (main.go -s)" "White"
    Write-ColorOutput "  .\hot_reload.ps1 -Client            # 客户端模式 (main.go -c)" "White"
    Write-ColorOutput "  .\hot_reload.ps1 -Config custom.toml # 使用自定义配置文件" "White"
    Write-ColorOutput "  .\hot_reload.ps1 -Help              # 显示此帮助信息" "White"
    Write-ColorOutput ""
    Write-ColorOutput "说明:" "Yellow"
    Write-ColorOutput "  - 脚本会自动检查并安装Air工具" "Gray"
    Write-ColorOutput "  - 自动创建.air.toml配置文件（如果不存在）" "Gray"
    Write-ColorOutput "  - 支持服务端和客户端模式的热重载" "Gray"
    exit 0
}

if ($Help) {
    Show-Help
}

# 检查Go是否安装
function Test-GoInstallation {
    try {
        $goVersion = go version 2>$null
        if ($goVersion) {
            Write-ColorOutput "✓ Go已安装: $goVersion" "Green"
            return $true
        }
    } catch {
        Write-ColorOutput "✗ 未找到Go安装" "Red"
        Write-ColorOutput "请先安装Go: https://golang.org/dl/" "Yellow"
        return $false
    }
    return $false
}

# 检查Air是否安装
function Test-AirInstallation {
    try {
        $airVersion = air -v 2>$null
        if ($airVersion) {
            Write-ColorOutput "✓ Air已安装: $airVersion" "Green"
            return $true
        }
    } catch {
        return $false
    }
    return $false
}

# 安装Air
function Install-Air {
    Write-ColorOutput "正在安装Air..." "Yellow"
    try {
        $env:GOPROXY = "https://goproxy.cn,direct"
        go install github.com/air-verse/air@latest
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✓ Air安装成功" "Green"
            return $true
        } else {
            Write-ColorOutput "✗ Air安装失败" "Red"
            return $false
        }
    } catch {
        Write-ColorOutput "✗ Air安装失败: $($_.Exception.Message)" "Red"
        return $false
    }
}

# 创建Air配置文件
function New-AirConfig {
    param([string]$ConfigPath, [string]$RunMode = "")
    
    $buildCmd = "go build -o ./tmp/main ."
    $runCmd = "./tmp/main"
    
    if ($RunMode -eq "server") {
        $runCmd = "./tmp/main -s"
    } elseif ($RunMode -eq "client") {
        $runCmd = "./tmp/main -c"
    }

    $configContent = @"
root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  args_bin = []
  bin = "$runCmd"
  cmd = "$buildCmd"
  delay = 1000
  exclude_dir = ["assets", "tmp", "vendor", "testdata", "vue", "web", "dist"]
  exclude_file = []
  exclude_regex = ["_test.go"]
  exclude_unchanged = false
  follow_symlink = false
  full_bin = ""
  include_dir = []
  include_ext = ["go", "tpl", "tmpl", "html"]
  include_file = []
  kill_delay = "0s"
  log = "build-errors.log"
  poll = false
  poll_interval = 0
  rerun = false
  rerun_delay = 500
  send_interrupt = false
  stop_on_root = false

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  main_only = false
  time = false

[misc]
  clean_on_exit = false

[screen]
  clear_on_rebuild = false
  keep_scroll = true
"@

    try {
        $configContent | Out-File -FilePath $ConfigPath -Encoding UTF8
        Write-ColorOutput "✓ 已创建Air配置文件: $ConfigPath" "Green"
        return $true
    } catch {
        Write-ColorOutput "✗ 创建配置文件失败: $($_.Exception.Message)" "Red"
        return $false
    }
}

# 主函数
function Main {
    Write-ColorOutput "=== Go热加载开发环境启动 ===" "Cyan"
    
    # 检查Go安装
    if (-not (Test-GoInstallation)) {
        exit 1
    }
    
    # 检查Air安装
    if (-not (Test-AirInstallation)) {
        Write-ColorOutput "Air未安装，正在自动安装..." "Yellow"
        if (-not (Install-Air)) {
            exit 1
        }
    }
    
    # 确定运行模式
    $runMode = ""
    if ($Server) {
        $runMode = "server"
        Write-ColorOutput "运行模式: 服务端 (-s)" "Cyan"
    } elseif ($Client) {
        $runMode = "client"
        Write-ColorOutput "运行模式: 客户端 (-c)" "Cyan"
    } else {
        Write-ColorOutput "运行模式: 默认" "Cyan"
    }
    
    # 检查配置文件
    if (-not (Test-Path $Config)) {
        Write-ColorOutput "配置文件不存在，正在创建: $Config" "Yellow"
        if (-not (New-AirConfig -ConfigPath $Config -RunMode $runMode)) {
            exit 1
        }
    } else {
        Write-ColorOutput "✓ 使用配置文件: $Config" "Green"
    }
    
    # 创建tmp目录
    if (-not (Test-Path "tmp")) {
        New-Item -ItemType Directory -Path "tmp" | Out-Null
        Write-ColorOutput "✓ 已创建tmp目录" "Green"
    }
    
    Write-ColorOutput ""
    Write-ColorOutput "启动Air热重载..." "Green"
    Write-ColorOutput "按 Ctrl+C 停止开发服务器" "Yellow"
    Write-ColorOutput "================================" "Cyan"
    
    # 启动Air
    try {
        if ($Config -eq ".air.toml") {
            air
        } else {
            air -c $Config
        }
    } catch {
        Write-ColorOutput "✗ Air启动失败: $($_.Exception.Message)" "Red"
        exit 1
    }
}

# 运行主函数
Main
