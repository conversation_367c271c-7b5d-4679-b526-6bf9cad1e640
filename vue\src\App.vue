<script setup>
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import duration from 'dayjs/plugin/duration';
import relativeTime from 'dayjs/plugin/relativeTime';
import { computed, onMounted, onUnmounted, reactive, ref } from 'vue';

dayjs.extend(duration);
dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

const currentTime = ref(new Date().toLocaleString());
const serverInfo = ref('');
const searchTerm = ref('');
const serviceTypeFilter = ref('all');
const statusFilter = ref('all');
const refreshing = ref(false);
const currentTab = ref('supervisor');

const services = reactive({
  supervisor: [],
  systemd: [],
  docker: [],
});

const stats = reactive({
  cpuUsage: '加载中...',
  cpuInfo: '获取中...',
  memoryUsage: '加载中...',
  memoryInfo: '获取中...',
  diskUsage: '加载中...',
  diskInfo: '获取中...',
  uploadSpeed: '获取中...',
  downloadSpeed: '获取中...',
  networkBar: '0%',
  networkInfo: '获取中...',
});

const filteredServices = computed(() => {
  const type = currentTab.value;
  const currentServiceList = services[type] || [];

  if (!Array.isArray(currentServiceList)) {
    return [];
  }

  return currentServiceList.filter(service => {
    const matchesSearch = service.name
      .toLowerCase()
      .includes(searchTerm.value.toLowerCase());
    const matchesType =
      serviceTypeFilter.value === 'all' || serviceTypeFilter.value === type;
    const matchesStatus =
      statusFilter.value === 'all' ||
      (statusFilter.value === 'running' &&
        (service.status === 'running' || service.status === 'active')) ||
      (statusFilter.value === 'stopped' &&
        (service.status === 'stopped' || service.status === 'inactive')) ||
      (statusFilter.value === 'failed' &&
        (service.status === 'failed' || service.status === 'error'));
    return matchesSearch && matchesType && matchesStatus;
  });
});

function getStatusClass(status) {
  if (status === 'running' || status === 'active') return 'status-running';
  if (status === 'stopped' || status === 'inactive') return 'status-stopped';
  if (status === 'failed' || status === 'error') return 'status-failed';
  return 'status-unknown';
}

function getStatusText(status) {
  if (status === 'running' || status === 'active') return '运行中';
  if (status === 'stopped' || status === 'inactive') return '已停止';
  if (status === 'failed' || status === 'error') return '失败';
  return '未知';
}

function getStatusTextClass(status) {
  if (status === 'running' || status === 'active') return 'text-green-600';
  if (status === 'stopped' || status === 'inactive') return 'text-red-600';
  if (status === 'failed' || status === 'error') return 'text-yellow-600';
  return 'text-gray-600';
}

function formatUptime(seconds) {
  if (typeof seconds !== 'number' || seconds < 0) {
    return '--';
  }
  const duration = dayjs.duration(seconds, 'seconds');
  if (seconds < 60) {
    return `${seconds} 秒`;
  } else if (seconds < 3600) {
    return `${duration.minutes()} 分钟`;
  } else if (seconds < 86400) {
    return `${duration.hours()} 小时 ${duration.minutes()} 分钟`;
  } else {
    return `${duration.days()} 天 ${duration.hours()} 小时 ${duration.minutes()} 分钟`;
  }
}

function logout() {
  window.location.href = '/login';
}

async function fetchServices(type) {
  const serverId = window.currentServerId;
  if (!serverId) {
    console.warn('Server ID not found. Cannot fetch services.');
    services[type] = [];
    return;
  }
  try {
    const response = await fetch(
      `/api/services/list?serverId=${serverId}&serviceType=${type}`,
      {
        credentials: 'include',
      }
    );
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    services[type] = data;
  } catch (error) {
    console.error(`Error fetching ${type} services:`, error);
    services[type] = [];
  }
}

async function switchTab(tab) {
  currentTab.value = tab;
  await fetchServices(tab);
}

async function refreshServices() {
  refreshing.value = true;
  await fetchServices(currentTab.value);
  setTimeout(() => {
    refreshing.value = false;
  }, 500);
}

async function serviceAction(action, name, type) {
  const serverId = window.currentServerId || 'default';
  const serverName = window.currentServerName || 'Unknown Server';
  try {
    const response = await fetch(`/api/services/${type}/${action}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        serverId: parseInt(serverId),
        serviceName: name,
        serviceType: type,
      }),
    });
    if (response.ok) {
      alert(`成功${action} ${type} 服务: ${name}\n服务器: ${serverName}`);
      await fetchServices(type);
    } else {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
  } catch (error) {
    console.error(`Error ${action} service:`, error);
    alert(`${action}服务失败: ${error.message}`);
  }
}

function startService(name, type) {
  serviceAction('start', name, type);
}

function stopService(name, type) {
  serviceAction('stop', name, type);
}

function restartService(name, type) {
  serviceAction('restart', name, type);
}

async function viewLogs(name, type) {
  const serverId = window.currentServerId || 'default';
  const serverName = window.currentServerName || 'Unknown Server';
  try {
    const response = await fetch(
      `/api/services/logs?serverId=${serverId}&serviceName=${name}&serviceType=${type}`,
      {
        credentials: 'include',
      }
    );
    if (response.ok) {
      const logs = await response.text();
      const logWindow = window.open('', '_blank', 'width=800,height=600');
      logWindow.document.write(`
        <html>
          <head>
            <title>${type} 服务日志 - ${name} (${serverName})</title>
            <style>
              body { font-family: monospace; padding: 20px; background: #1a1a1a; color: #00ff00; }
              pre { white-space: pre-wrap; word-wrap: break-word; }
            </style>
          </head>
          <body>
            <h3>${type} 服务日志 - ${name}</h3>
            <p>服务器: ${serverName} (ID: ${serverId})</p>
            <hr>
            <pre>${logs}</pre>
          </body>
        </html>
      `);
    } else {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
  } catch (error) {
    console.error('Error viewing logs:', error);
    alert(`查看日志失败: ${error.message}`);
  }
}

let ws = null;
let wsReconnectAttempts = 0;
const maxReconnectAttempts = 5;

function initWebSocket() {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  const wsUrl = `${protocol}//${window.location.host}/ws/frontend`;
  ws = new WebSocket(wsUrl);

  ws.onopen = () => {
    wsReconnectAttempts = 0;
    ws.send(
      JSON.stringify({ type: 'auth', timestamp: new Date().toISOString() })
    );
  };

  ws.onmessage = event => {
    try {
      const message = JSON.parse(event.data);
      handleWebSocketMessage(message);
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  };

  ws.onclose = () => {
    if (wsReconnectAttempts < maxReconnectAttempts) {
      wsReconnectAttempts++;
      setTimeout(initWebSocket, 3000);
    } else {
      startHttpPolling();
    }
  };

  ws.onerror = error => {
    console.error('WebSocket error:', error);
  };
}

function handleWebSocketMessage(message) {
  switch (message.type) {
    case 'auth_response':
      if (message.error) {
        startHttpPolling();
      } else {
        const serverId = getUrlParameter('mid') || 1;
        ws.send(
          JSON.stringify({
            type: 'subscribe',
            server_id: parseInt(serverId),
            timestamp: new Date().toISOString(),
          })
        );
      }
      break;
    case 'system_stats_broadcast':
      updateSystemStats(message.data);
      break;
  }
}

async function fetchSystemStats() {
  try {
    const mid = getUrlParameter('mid') || '1';
    const response = await fetch(`/api/system/stats?serverId=${mid}`, {
      credentials: 'include',
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    updateSystemStats(data);
  } catch (error) {
    stats.cpuUsage = '获取失败';
    stats.memoryUsage = '获取失败';
    stats.diskUsage = '获取失败';
    stats.uploadSpeed = '获取失败';
    stats.downloadSpeed = '获取失败';
  }
}

function startHttpPolling() {
  setInterval(fetchSystemStats, 10000);
}

function updateSystemStats(data) {
  stats.cpuUsage = `${data.CPU.toFixed(2)}%`;
  stats.cpuInfo = '实时数据';

  const memPercent = ((data.MemUsed / data.MemTotal) * 100).toFixed(2);
  const memUsedGB = (data.MemUsed / 1024 / 1024 / 1024).toFixed(1);
  const memTotalGB = (data.MemTotal / 1024 / 1024 / 1024).toFixed(1);
  stats.memoryUsage = `${memPercent}%`;
  stats.memoryInfo = `${memUsedGB}GB / ${memTotalGB}GB`;

  let diskPercent = 0;
  let diskUsedGB = 0;
  let diskTotalGB = 0;
  if (data.DiskTotal > 0) {
    diskPercent = ((data.DiskUsed / data.DiskTotal) * 100).toFixed(2);
    diskUsedGB = (data.DiskUsed / 1024 / 1024 / 1024).toFixed(0);
    diskTotalGB = (data.DiskTotal / 1024 / 1024 / 1024).toFixed(0);
  }
  stats.diskUsage = `${diskPercent}%`;
  stats.diskInfo = `${diskUsedGB}GB / ${diskTotalGB}GB`;

  const netInMB = (data.NetInTransfer / 1024 / 1024).toFixed(1);
  const netOutMB = (data.NetOutTransfer / 1024 / 1024).toFixed(1);
  stats.uploadSpeed = `${netOutMB} MB`;
  stats.downloadSpeed = `${netInMB} MB`;
  stats.networkInfo = '累计传输量';
  stats.networkBar = '30%';
}

function getUrlParameter(name) {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(name);
}

function initializeServerInfo() {
  const mid = getUrlParameter('mid');
  const serverName = getUrlParameter('server');
  if (mid && serverName) {
    serverInfo.value = `正在管理服务器: <strong>${decodeURIComponent(
      serverName
    )}</strong> (ID: ${mid})`;
    document.title = `服务管理 - ${decodeURIComponent(
      serverName
    )} - 服务器监控系统`;
    window.currentServerId = mid;
    window.currentServerName = decodeURIComponent(serverName);
  } else {
    serverInfo.value = `请从仪表板选择服务器以管理服务。`;
    window.currentServerId = null;
  }
}

onMounted(() => {
  initializeServerInfo();
  initWebSocket();
  switchTab('supervisor');
  setInterval(() => {
    currentTime.value = new Date().toLocaleString();
  }, 1000);
  setInterval(async () => {
    await fetchServices(currentTab.value);
  }, 30000);
});

onUnmounted(() => {
  if (ws) {
    ws.close();
  }
});
</script>

<template>
  <div
    class="bg-gradient-to-br from-white via-gray-50 to-gray-100 min-h-screen"
  >
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <h1 class="text-xl font-bold text-gray-900">服务器监控系统</h1>
            </div>
            <div class="hidden md:ml-6 md:flex md:space-x-8">
              <a
                href="/dashboard"
                class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >仪表板</a
              >
              <a
                href="#"
                class="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >服务管理</a
              >
              <a
                href="system-example.html"
                class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >系统监控</a
              >
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-500">{{ currentTime }}</span>
            <button
              @click="logout"
              class="text-gray-500 hover:text-red-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
              title="注销"
            >
              注销
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- 页面标题 -->
        <div class="mb-8">
          <div
            class="flex flex-col md:flex-row md:items-center md:justify-between"
          >
            <div>
              <h2 class="text-3xl font-bold text-gray-900">服务管理</h2>
              <p class="mt-2 text-gray-600" v-html="serverInfo"></p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-wrap gap-3">
              <!-- 搜索框 -->
              <div class="relative">
                <input
                  type="text"
                  v-model="searchTerm"
                  placeholder="搜索服务名称..."
                  class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
                />
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <svg
                    class="h-5 w-5 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    ></path>
                  </svg>
                </div>
              </div>
              <!-- 服务类型过滤 -->
              <select
                v-model="serviceTypeFilter"
                class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">所有服务</option>
                <option value="supervisor">Supervisor</option>
                <option value="systemd">Systemd</option>
                <option value="docker">Docker</option>
              </select>
              <!-- 状态过滤 -->
              <select
                v-model="statusFilter"
                class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">所有状态</option>
                <option value="running">运行中</option>
                <option value="stopped">已停止</option>
                <option value="failed">失败</option>
              </select>
              <!-- 刷新按钮 -->
              <button
                @click="refreshServices"
                class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                <svg
                  class="w-5 h-5"
                  :class="{ 'animate-spin-custom': refreshing }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- 统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- CPU 使用率 -->
          <div class="metric-card bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">CPU 使用率</h3>
              <div
                class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-blue-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
              </div>
            </div>
            <div class="mb-2">
              <span class="text-3xl font-bold text-gray-900">{{
                stats.cpuUsage
              }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                class="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full progress-bar"
                :style="{ width: stats.cpuUsage }"
              ></div>
            </div>
            <p class="text-sm text-gray-600">{{ stats.cpuInfo }}</p>
          </div>

          <!-- 内存使用率 -->
          <div class="metric-card bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">内存使用率</h3>
              <div
                class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-green-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                  ></path>
                </svg>
              </div>
            </div>
            <div class="mb-2">
              <span class="text-3xl font-bold text-gray-900">{{
                stats.memoryUsage
              }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full progress-bar"
                :style="{ width: stats.memoryUsage }"
              ></div>
            </div>
            <p class="text-sm text-gray-600">{{ stats.memoryInfo }}</p>
          </div>
        </div>

        <!-- 第二行：磁盘使用率 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- 磁盘使用率 -->
          <div class="metric-card bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">磁盘使用率</h3>
              <div
                class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-yellow-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
            </div>
            <div class="mb-2">
              <span class="text-3xl font-bold text-gray-900">{{
                stats.diskUsage
              }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                class="bg-gradient-to-r from-yellow-400 to-yellow-600 h-2 rounded-full progress-bar"
                :style="{ width: stats.diskUsage }"
              ></div>
            </div>
            <p class="text-sm text-gray-600">{{ stats.diskInfo }}</p>
          </div>

          <!-- 网络流量 -->
          <div class="metric-card bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">网络流量</h3>
              <div
                class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-purple-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
            </div>
            <div class="mb-2">
              <div class="flex justify-between text-sm">
                <span class="text-gray-600"
                  >↑ <span>{{ stats.uploadSpeed }}</span></span
                >
                <span class="text-gray-600"
                  >↓ <span>{{ stats.downloadSpeed }}</span></span
                >
              </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                class="bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full progress-bar"
                :style="{ width: stats.networkBar }"
              ></div>
            </div>
            <p class="text-sm text-gray-600">{{ stats.networkInfo }}</p>
          </div>
        </div>

        <!-- 服务管理标签页 -->
        <div class="bg-white rounded-lg shadow-md">
          <!-- 标签页头部 -->
          <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              <button
                @click="switchTab('supervisor')"
                :class="[
                  'tab-button',
                  {
                    'border-blue-500 text-blue-600':
                      currentTab === 'supervisor',
                    'border-transparent text-gray-500':
                      currentTab !== 'supervisor',
                  },
                  'hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
                ]"
              >
                Supervisor 服务
              </button>
              <button
                @click="switchTab('systemd')"
                :class="[
                  'tab-button',
                  {
                    'border-blue-500 text-blue-600': currentTab === 'systemd',
                    'border-transparent text-gray-500':
                      currentTab !== 'systemd',
                  },
                  'hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
                ]"
              >
                Systemd 服务
              </button>
              <button
                @click="switchTab('docker')"
                :class="[
                  'tab-button',
                  {
                    'border-blue-500 text-blue-600': currentTab === 'docker',
                    'border-transparent text-gray-500': currentTab !== 'docker',
                  },
                  'hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
                ]"
              >
                Docker 容器
              </button>
            </nav>
          </div>

          <!-- 标签页内容 -->
          <div class="p-6">
            <div v-if="currentTab === 'supervisor'">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div
                  v-for="service in filteredServices"
                  :key="service.name"
                  class="service-card bg-gray-50 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-all duration-300"
                >
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                      <span
                        :class="[
                          'status-indicator',
                          getStatusClass(service.status),
                        ]"
                      ></span>
                      <h3 class="text-lg font-semibold text-gray-900">
                        {{ service.name }}
                      </h3>
                    </div>
                    <span
                      :class="[
                        'text-sm',
                        'font-medium',
                        getStatusTextClass(service.status),
                      ]"
                      >{{ getStatusText(service.status) }}</span
                    >
                  </div>
                  <p class="text-sm text-gray-600 mb-3">
                    {{ service.description || '无描述' }}
                  </p>
                  <div class="grid grid-cols-2 gap-4 mb-3 text-sm">
                    <div>
                      <span class="font-medium text-gray-700">PID:</span>
                      <span class="text-gray-600">{{
                        service.pid || '--'
                      }}</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-700">运行时间:</span>
                      <span class="text-gray-600">{{
                        formatUptime(service.uptime)
                      }}</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-700">内存:</span>
                      <span class="text-gray-600">{{
                        service.memory || '--'
                      }}</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-700">CPU:</span>
                      <span class="text-gray-600">{{
                        service.cpu || '--'
                      }}</span>
                    </div>
                  </div>
                  <div class="flex space-x-2">
                    <template v-if="service.status === 'running'">
                      <button
                        @click="stopService(service.name, 'supervisor')"
                        class="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors"
                      >
                        停止
                      </button>
                      <button
                        @click="restartService(service.name, 'supervisor')"
                        class="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 transition-colors"
                      >
                        重启
                      </button>
                    </template>
                    <template v-else>
                      <button
                        @click="startService(service.name, 'supervisor')"
                        class="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600 transition-colors"
                      >
                        启动
                      </button>
                    </template>
                    <button
                      @click="viewLogs(service.name, 'supervisor')"
                      class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
                    >
                      日志
                    </button>
                  </div>
                </div>
                <div
                  v-if="filteredServices.length === 0"
                  class="col-span-full text-center py-12 text-gray-500"
                >
                  {{
                    services.supervisor.length === 0
                      ? '没有找到 supervisor 服务。'
                      : '没有符合筛选条件的服务。'
                  }}
                </div>
              </div>
            </div>
            <div v-if="currentTab === 'systemd'">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div
                  v-for="service in filteredServices"
                  :key="service.name"
                  class="service-card bg-gray-50 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-all duration-300"
                >
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                      <span
                        :class="[
                          'status-indicator',
                          getStatusClass(service.status),
                        ]"
                      ></span>
                      <h3 class="text-lg font-semibold text-gray-900">
                        {{ service.name }}
                      </h3>
                    </div>
                    <span
                      :class="[
                        'text-sm',
                        'font-medium',
                        getStatusTextClass(service.status),
                      ]"
                      >{{ getStatusText(service.status) }}</span
                    >
                  </div>
                  <p class="text-sm text-gray-600 mb-3">
                    {{ service.description || '无描述' }}
                  </p>
                  <div class="grid grid-cols-2 gap-4 mb-3 text-sm">
                    <div>
                      <span class="font-medium text-gray-700">PID:</span>
                      <span class="text-gray-600">{{
                        service.pid || '--'
                      }}</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-700">运行时间:</span>
                      <span class="text-gray-600">{{
                        formatUptime(service.uptime)
                      }}</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-700">内存:</span>
                      <span class="text-gray-600">{{
                        service.memory || '--'
                      }}</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-700">CPU:</span>
                      <span class="text-gray-600">{{
                        service.cpu || '--'
                      }}</span>
                    </div>
                  </div>
                  <div class="flex space-x-2">
                    <template v-if="service.status === 'running'">
                      <button
                        @click="stopService(service.name, 'systemd')"
                        class="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors"
                      >
                        停止
                      </button>
                      <button
                        @click="restartService(service.name, 'systemd')"
                        class="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 transition-colors"
                      >
                        重启
                      </button>
                    </template>
                    <template v-else>
                      <button
                        @click="startService(service.name, 'systemd')"
                        class="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600 transition-colors"
                      >
                        启动
                      </button>
                    </template>
                    <button
                      @click="viewLogs(service.name, 'systemd')"
                      class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
                    >
                      日志
                    </button>
                  </div>
                </div>
                <div
                  v-if="filteredServices.length === 0"
                  class="col-span-full text-center py-12 text-gray-500"
                >
                  {{
                    services.systemd.length === 0
                      ? '没有找到 systemd 服务。'
                      : '没有符合筛选条件的服务。'
                  }}
                </div>
              </div>
            </div>
            <div v-if="currentTab === 'docker'">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div
                  v-for="service in filteredServices"
                  :key="service.name"
                  class="service-card bg-gray-50 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-all duration-300"
                >
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                      <span
                        :class="[
                          'status-indicator',
                          getStatusClass(service.status),
                        ]"
                      ></span>
                      <h3 class="text-lg font-semibold text-gray-900">
                        {{ service.name }}
                      </h3>
                    </div>
                    <span
                      :class="[
                        'text-sm',
                        'font-medium',
                        getStatusTextClass(service.status),
                      ]"
                      >{{ getStatusText(service.status) }}</span
                    >
                  </div>
                  <p class="text-sm text-gray-600 mb-3">
                    {{ service.description || '无描述' }}
                  </p>
                  <div class="grid grid-cols-2 gap-4 mb-3 text-sm">
                    <div>
                      <span class="font-medium text-gray-700">ID:</span>
                      <span class="text-gray-600">{{
                        service.id.substring(0, 12) || '--'
                      }}</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-700">运行时间:</span>
                      <span class="text-gray-600">{{
                        formatUptime(service.uptime)
                      }}</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-700">内存:</span>
                      <span class="text-gray-600">{{
                        service.memory || '--'
                      }}</span>
                    </div>
                    <div>
                      <span class="font-medium text-gray-700">CPU:</span>
                      <span class="text-gray-600">{{
                        service.cpu || '--'
                      }}</span>
                    </div>
                  </div>
                  <div class="flex space-x-2">
                    <template v-if="service.status === 'running'">
                      <button
                        @click="stopService(service.name, 'docker')"
                        class="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors"
                      >
                        停止
                      </button>
                      <button
                        @click="restartService(service.name, 'docker')"
                        class="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 transition-colors"
                      >
                        重启
                      </button>
                    </template>
                    <template v-else>
                      <button
                        @click="startService(service.name, 'docker')"
                        class="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600 transition-colors"
                      >
                        启动
                      </button>
                    </template>
                    <button
                      @click="viewLogs(service.name, 'docker')"
                      class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
                    >
                      日志
                    </button>
                  </div>
                </div>
                <div
                  v-if="filteredServices.length === 0"
                  class="col-span-full text-center py-12 text-gray-500"
                >
                  {{
                    services.docker.length === 0
                      ? '没有找到 docker 容器。'
                      : '没有符合筛选条件的容器。'
                  }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped>
.service-card {
  border: 1px solid #e5e7eb;
}

.service-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 6px;
}

.status-running {
  background-color: #10b981;
}

.status-stopped {
  background-color: #ef4444;
}

.status-failed {
  background-color: #f59e0b;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

@media (min-width: 1024px) {
  .service-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .service-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.animate-spin-custom {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
