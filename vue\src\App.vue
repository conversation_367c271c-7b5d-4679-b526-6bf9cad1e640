<script setup>
import { onMounted, onUnmounted, watch } from 'vue';
import { RouterView, useRoute } from 'vue-router';
import NotificationDisplay from './components/NotificationDisplay.vue'; // Import the new component

const route = useRoute();

// 简化的路由监听
watch(
  () => route.path,
  (newPath, oldPath) => {
    console.log(`🚦 路由变化: ${oldPath} -> ${newPath}`);
  },
  { immediate: true }
);

onMounted(() => {
  console.log('🎨 App.vue 已挂载，服务器监控系统已启动');
});

onUnmounted(() => {
  console.log('🎨 App.vue 已卸载');
});
</script>

<template>
  <RouterView />
  <NotificationDisplay />
  <!-- Add the notification display component -->
</template>

<style></style>
