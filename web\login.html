<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>登录 - 服务器监控系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      .login-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }
      .login-form {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }
      .input-field {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
      }
      .input-field::placeholder {
        color: rgba(255, 255, 255, 0.7);
      }
      .input-field:focus {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        outline: none;
      }
    </style>
  </head>
  <body>
    <div class="login-container flex items-center justify-center">
      <div class="login-form rounded-lg p-8 w-full max-w-md mx-4">
        <div class="text-center mb-8">
          <h1 class="text-3xl font-bold text-white mb-2">服务器监控系统</h1>
          <p class="text-white/80">请登录以继续</p>
        </div>

        <form id="loginForm" class="space-y-6">
          <div>
            <label
              for="username"
              class="block text-white text-sm font-medium mb-2"
              >用户名</label
            >
            <input
              type="text"
              id="username"
              name="username"
              required
              class="input-field w-full px-4 py-3 rounded-lg transition-all duration-200"
              placeholder="请输入用户名"
            />
          </div>

          <div>
            <label
              for="password"
              class="block text-white text-sm font-medium mb-2"
              >密码</label
            >
            <input
              type="password"
              id="password"
              name="password"
              required
              class="input-field w-full px-4 py-3 rounded-lg transition-all duration-200"
              placeholder="请输入密码"
            />
          </div>

          <button
            type="submit"
            id="loginBtn"
            class="w-full bg-white text-purple-600 font-semibold py-3 px-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
          >
            登录
          </button>
        </form>

        <div
          id="errorMessage"
          class="mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-200 text-sm hidden"
        ></div>

        <div
          id="loadingMessage"
          class="mt-4 p-3 bg-blue-500/20 border border-blue-500/30 rounded-lg text-blue-200 text-sm hidden"
        >
          正在登录...
        </div>
      </div>
    </div>

    <script>
      document
        .getElementById('loginForm')
        .addEventListener('submit', async function (e) {
          e.preventDefault();

          const username = document.getElementById('username').value;
          const password = document.getElementById('password').value;
          const loginBtn = document.getElementById('loginBtn');
          const errorMessage = document.getElementById('errorMessage');
          const loadingMessage = document.getElementById('loadingMessage');

          // Hide previous messages
          errorMessage.classList.add('hidden');
          loadingMessage.classList.remove('hidden');
          loginBtn.disabled = true;
          loginBtn.textContent = '登录中...';

          try {
            const response = await fetch('/api/login', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                username: username,
                password: password,
              }),
            });

            const data = await response.json();

            if (data.success) {
              // Token is now stored in httpOnly cookie by the server
              // Redirect to dashboard
              window.location.href = '/dashboard';
            } else {
              showError(data.message || '登录失败');
            }
          } catch (error) {
            console.error('Login error:', error);
            showError('网络错误，请稍后重试');
          } finally {
            loadingMessage.classList.add('hidden');
            loginBtn.disabled = false;
            loginBtn.textContent = '登录';
          }
        });

      function showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
      }

      // Check if already logged in
      window.addEventListener('load', function () {
        // Verify if user is already authenticated by making a test request
        // The server will check the httpOnly cookie automatically
        fetch('/api/servers', {
          credentials: 'include', // Include cookies in the request
        })
          .then(response => {
            if (response.ok) {
              // User is authenticated, redirect to dashboard
              window.location.href = '/dashboard';
            }
          })
          .catch(error => {
            // User is not authenticated, stay on login page
            console.log('User not authenticated');
          });
      });
    </script>
  </body>
</html>
