<template>
  <div
    v-if="notification.show"
    :class="['notification-container', notification.type]"
  >
    <p>{{ notification.message }}</p>
    <button @click="notification.hideNotification">X</button>
  </div>
</template>

<script setup>
import { useNotificationStore } from '../store/notification';

const notification = useNotificationStore();
</script>

<style scoped>
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  min-width: 250px;
  max-width: 400px;
  word-wrap: break-word;
}

.notification-container p {
  margin: 0;
  flex-grow: 1;
  padding-right: 10px;
}

.notification-container button {
  background: none;
  border: none;
  color: white;
  font-size: 1.2em;
  cursor: pointer;
  padding: 0 5px;
  margin-left: 10px;
}

.notification-container button:hover {
  opacity: 0.8;
}

.notification-container.info {
  background-color: #2196f3; /* Blue */
}

.notification-container.success {
  background-color: #4caf50; /* Green */
}

.notification-container.warning {
  background-color: #ff9800; /* Orange */
}

.notification-container.error {
  background-color: #f44336; /* Red */
}
</style>
