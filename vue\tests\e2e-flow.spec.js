import { test, expect } from '@playwright/test';

test.describe('端到端用户流程测试', () => {
  test('完整的用户使用流程', async ({ page }) => {
    // 1. 访问根路径，应该重定向到仪表板
    await page.goto('/');
    
    // 由于未登录，路由守卫应该重定向到登录页
    await expect(page).toHaveURL('/login');
    
    // 2. 登录流程
    await expect(page.locator('h1')).toContainText('服务器监控系统');
    
    // 模拟登录API
    await page.route('/api/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true, user: { username: 'admin' } })
      });
    });
    
    // 模拟服务器数据API
    await page.route('/api/servers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 1,
            name: '生产服务器',
            ipv4: '*************',
            host: {
              Platform: 'Linux',
              PlatformVersion: 'Ubuntu 20.04',
              MemTotal: 8589934592
            },
            status: {
              CPU: 45.5,
              MemUsed: 4294967296,
              Uptime: 86400,
              NetInSpeed: 1024000,
              NetOutSpeed: 512000
            },
            last_active: Math.floor(Date.now() / 1000) - 30
          },
          {
            id: 2,
            name: '测试服务器',
            ipv4: '*************',
            host: {
              Platform: 'Windows',
              PlatformVersion: 'Windows Server 2019',
              MemTotal: 16777216000
            },
            status: {
              CPU: 85.0, // 高CPU使用率，应该显示警告状态
              MemUsed: 15032385536,
              Uptime: 172800
            },
            last_active: Math.floor(Date.now() / 1000) - 15
          }
        ])
      });
    });
    
    // 填写登录信息
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'password123');
    await page.click('button:has-text("登录")');
    
    // 3. 验证跳转到仪表板
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('h1')).toContainText('服务器监控系统');
    
    // 4. 验证仪表板数据显示
    await page.waitForTimeout(1000); // 等待数据加载
    
    // 检查统计概览
    const statsCards = page.locator('.stat-card');
    await expect(statsCards).toHaveCount(4);
    
    // 检查服务器卡片
    const serverCards = page.locator('.server-card');
    await expect(serverCards).toHaveCount(12);
    
    // 检查第一个服务器（在线状态）
    const firstCard = serverCards.first();
    await expect(firstCard).toContainText('生产服务器');
    await expect(firstCard).toContainText('*************');
    await expect(firstCard.locator('.status-online')).toBeVisible();
    
    // 检查第二个服务器（警告状态，因为CPU > 85%）
    const secondCard = serverCards.nth(1);
    await expect(secondCard).toContainText('测试服务器');
    await expect(secondCard.locator('.status-warning')).toBeVisible();
    
    // 5. 测试搜索功能
    await page.fill('input[placeholder="搜索服务器..."]', '生产');
    
    // 验证搜索结果
    const visibleCards = page.locator('.server-card:visible');
    await expect(visibleCards).toHaveCount(1);
    await expect(visibleCards.first()).toContainText('生产服务器');
    
    // 清空搜索
    await page.fill('input[placeholder="搜索服务器..."]', '');
    
    // 6. 点击服务器卡片跳转到服务管理
    await page.click('.server-card:first-child');
    await expect(page).toHaveURL('/services/1');
    
    // 7. 验证服务管理页面
    // 模拟系统统计API
    await page.route('/api/system/stats*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          CPU: 45.5,
          MemUsed: 4294967296,
          MemTotal: 8589934592,
          DiskUsed: 107374182400,
          DiskTotal: 214748364800
        })
      });
    });
    
    // 模拟服务列表API
    await page.route('/api/services/list*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          { name: 'nginx', status: 'running', description: 'Web Server' },
          { name: 'mysql', status: 'stopped', description: 'Database Server' },
          { name: 'redis', status: 'running', description: 'Cache Server' }
        ])
      });
    });
    
    await expect(page.locator('h1')).toContainText('服务管理');
    
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 检查系统统计
    const serviceStatsCards = page.locator('.stat-card');
    await expect(serviceStatsCards).toHaveCount(4);
    
    // 检查服务列表
    const serviceItems = page.locator('.service-item');
    await expect(serviceItems).toHaveCount(3);
    
    // 8. 测试服务操作
    // 模拟服务启动API
    await page.route('/api/services/supervisor/start', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });
    
    // 启动停止的MySQL服务
    const mysqlService = page.locator('.service-item:has-text("mysql")');
    await mysqlService.locator('button:has-text("启动")').click();
    
    // 验证成功消息
    await expect(page.locator('.el-message--success')).toBeVisible();
    
    // 9. 测试服务标签页切换
    await page.click('.el-tabs__item:has-text("Systemd")');
    await expect(page.locator('.el-tabs__item.is-active')).toContainText('Systemd');
    
    await page.click('.el-tabs__item:has-text("Docker")');
    await expect(page.locator('.el-tabs__item.is-active')).toContainText('Docker');
    
    // 10. 返回仪表板
    await page.goto('/dashboard');
    await expect(page).toHaveURL('/dashboard');
    
    // 11. 测试状态过滤
    await page.selectOption('select', 'warning');
    
    // 应该只显示警告状态的服务器
    const warningCards = page.locator('.server-card:visible');
    await expect(warningCards).toHaveCount(1);
    await expect(warningCards.first()).toContainText('测试服务器');
    
    // 重置过滤器
    await page.selectOption('select', 'all');
    
    // 12. 测试WebSocket连接模拟
    await page.evaluate(() => {
      // 模拟WebSocket实时数据更新
      const event = new CustomEvent('server-update', {
        detail: {
          id: 1,
          stats: {
            CPU: 30.0, // 降低CPU使用率
            MemUsed: 3221225472,
            NetInSpeed: 2048000,
            NetOutSpeed: 1024000
          }
        }
      });
      window.dispatchEvent(event);
    });
    
    // 验证数据更新
    await page.waitForTimeout(500);
    const updatedFirstCard = page.locator('.server-card').first();
    await expect(updatedFirstCard).toContainText('30%');
  });
  
  test('未授权访问处理', async ({ page }) => {
    // 模拟未授权的API响应
    await page.route('/api/servers', async route => {
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Unauthorized' })
      });
    });
    
    // 尝试直接访问仪表板
    await page.goto('/dashboard');
    
    // 应该被重定向到登录页
    await expect(page).toHaveURL('/login');
  });
  
  test('网络错误处理', async ({ page }) => {
    // 模拟网络错误
    await page.route('/api/servers', async route => {
      await route.abort('failed');
    });
    
    // 模拟登录成功但后续API失败
    await page.route('/api/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });
    
    await page.goto('/login');
    
    // 登录
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'password123');
    await page.click('button:has-text("登录")');
    
    // 跳转到仪表板但API失败
    await expect(page).toHaveURL('/dashboard');
    
    // 应该显示错误消息
    await expect(page.locator('.error-message')).toBeVisible();
  });
  
  test('浏览器兼容性测试', async ({ page, browserName }) => {
    // 根据不同浏览器调整测试
    console.log(`Running on ${browserName}`);
    
    // 模拟基本API
    await page.route('/api/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });
    
    await page.route('/api/servers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([])
      });
    });
    
    // 基本功能测试
    await page.goto('/login');
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'password123');
    await page.click('button:has-text("登录")');
    
    await expect(page).toHaveURL('/dashboard');
    
    // 检查CSS样式是否正确加载
    const loginContainer = page.locator('.login-container');
    if (browserName !== 'webkit') { // Safari可能有一些CSS兼容性问题
      await expect(loginContainer).toHaveCSS('background', /linear-gradient/);
    }
  });
});
