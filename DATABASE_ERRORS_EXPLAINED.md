# 数据库错误解释和解决方案

## 🔍 错误分析

您遇到的日志显示了两个主要的数据库问题：

### 1. **"record not found" 错误**

```
I:/agent_new/main.go:3056 record not found
SELECT * FROM `server_statuses` WHERE id = 2
```

**问题原因：**
- 您配置了两台服务器（ID 1 和 ID 2）
- 但数据库中只有 ID 1 的服务器记录
- ID 2 的服务器可能还没有连接过，或者数据库中缺少该记录

**解决方案：**
- 这是正常现象，系统会自动返回离线状态
- 当 ID 2 的服务器首次连接时，会自动创建记录

### 2. **"WHERE conditions required" 错误**

```
I:/agent_new/main.go:3160 WHERE conditions required
UPDATE `server_status_histories` SET ...
```

**问题原因：**
- `ServerStatusHistory` 表缺少主键
- GORM 无法确定要更新哪条记录
- 历史数据应该是插入新记录，而不是更新现有记录

**解决方案：**
- ✅ 已修复：为 `ServerStatusHistory` 添加了主键
- ✅ 已修复：使用 `Create` 而不是 `Save` 来插入历史数据

## 🛠️ 已实施的修复

### 1. **添加主键到历史表**

```go
type ServerStatusHistory struct {
    ID                uint      `gorm:"primaryKey"` // 新增主键
    ServerID          int       `gorm:"index"`
    Timestamp         time.Time `gorm:"index"`
    // ... 其他字段
}
```

### 2. **使用正确的数据库操作**

```go
// 历史数据使用 Create（插入新记录）
if err := createWithRetry(&serverStatusHistory, 3); err != nil {
    log.Printf("Database error (ServerStatusHistory) after retries: %v", err)
}

// 状态数据使用 Save（更新或插入）
if err := saveWithRetry(&serverStatus, 3); err != nil {
    log.Printf("Database error (ServerStatus) after retries: %v", err)
}
```

### 3. **添加专用的创建函数**

```go
func createWithRetry(data interface{}, maxRetries int) error {
    // 使用 db.Create() 而不是 db.Save()
    // 专门用于插入新记录
}
```

## 📊 系统行为说明

### 正常的日志模式

修复后，您应该看到：

```
✅ 正常日志：
Successfully saved service data for ServerID 1
Server ID 2 (gz) not found in database, returning default offline status.
```

### 服务器连接流程

1. **服务器 ID 1**：
   - 已连接并发送数据
   - 正常保存到数据库
   - 显示实时监控数据

2. **服务器 ID 2**：
   - 配置存在但未连接
   - 系统返回默认离线状态
   - 一旦连接会自动创建记录

## 🔧 配置检查

确保您的服务器配置正确：

### server.json
```json
{
  "servers": [
    {
      "mid": 1,
      "name": "Server-1",
      "host": "*************",
      "enabled": true
    },
    {
      "mid": 2,
      "name": "Server-2", 
      "host": "*************",
      "enabled": true
    }
  ]
}
```

### 客户端配置
确保第二台服务器的客户端配置了正确的 `mid: 2`：

```json
{
  "mid": 2,
  "server": "your_server_ip",
  "port": "8080",
  "password": "your_password"
}
```

## 🚀 重新部署

1. **重新编译程序**：
   ```bash
   go build -o agent_linux_amd64 .
   ```

2. **重启服务器**：
   ```bash
   ./agent_linux_amd64 -s -f server.json -key "your_jwt_key"
   ```

3. **连接客户端**：
   ```bash
   ./agent_linux_amd64 -c -f client.json
   ```

## 📈 预期结果

修复后，您应该看到：
- ✅ 没有 "WHERE conditions required" 错误
- ✅ 历史数据正常保存
- ✅ 服务器状态正常更新
- ✅ 前端显示正常

## 🔍 故障排除

如果问题仍然存在：

1. **删除旧数据库**：
   ```bash
   rm server.db*
   ```

2. **重新启动服务器**（会自动创建新的数据库结构）

3. **检查客户端连接**：
   - 确保网络连通性
   - 验证密码配置
   - 检查防火墙设置
