import { defineStore } from 'pinia';
import { ref } from 'vue';
import axios from 'axios';
import { useNotificationStore } from './notification';

export const useAuthStore = defineStore('auth', () => {
  const notificationStore = useNotificationStore();

  // 状态
  const isAuthenticated = ref(false);
  const user = ref(null);
  const loading = ref(false);

  // 检查认证状态
  const checkAuth = async () => {
    loading.value = true;
    try {
      const response = await axios.get('/api/servers', {
        withCredentials: true, // 包含credentials
      });

      if (response.status === 200) {
        isAuthenticated.value = true;
        // 可以从响应中获取用户信息
        startTokenRefreshTimer(); // 认证成功后启动定时刷新
        return true;
      } else {
        isAuthenticated.value = false;
        user.value = null;
        return false;
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      isAuthenticated.value = false;
      user.value = null;
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 登录
  const login = async credentials => {
    loading.value = true;
    try {
      const response = await axios.post('/api/login', credentials, {
        headers: {
          'Content-Type': 'application/json',
        },
        withCredentials: true, // 包含credentials
      });

      if (response.status === 200) {
        isAuthenticated.value = true;
        user.value = response.data.user || { username: credentials.username };
        startTokenRefreshTimer(); // 登录成功后启动定时刷新
        return { success: true };
      } else {
        return { success: false, error: response.data.error || '登录失败' };
      }
    } catch (error) {
      console.error('Login error:', error);
      let errorMessage = '网络错误，请稍后重试'; // Default error message
      if (error.response && error.response.data) {
        // Backend sent a structured error response
        errorMessage =
          error.response.data.message ||
          error.response.data.error ||
          errorMessage;
      } else if (error.message) {
        // Axios error message (e.g., network issues)
        errorMessage = error.message;
      }
      notificationStore.showNotification(errorMessage, 'error');
      return { success: false, error: errorMessage };
    } finally {
      loading.value = false;
    }
  };

  // 登出
  const logout = async () => {
    stopTokenRefreshTimer(); // 登出时停止定时刷新
    try {
      await axios.post(
        '/api/logout',
        {},
        {
          withCredentials: true,
        }
      );
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      isAuthenticated.value = false;
      user.value = null;
    }
  };

  // Token刷新定时器
  let refreshTimer = null;
  const TOKEN_REFRESH_INTERVAL = 15 * 60 * 1000; // 15分钟刷新一次 (JWT过期时间为20分钟)

  const startTokenRefreshTimer = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
    }
    refreshTimer = setInterval(async () => {
      console.log('Attempting to refresh token...');
      try {
        const response = await axios.post(
          '/api/refresh',
          {},
          {
            withCredentials: true,
          }
        );
        if (response.status === 200) {
          console.log('Token refreshed successfully.');
        } else {
          console.warn('Token refresh failed:', response.data.message);
          // 如果刷新失败，可能是token无效或已过期，执行登出
          logout();
        }
      } catch (error) {
        console.error('Token refresh error:', error);
        // 网络错误或服务器错误，执行登出
        logout();
      }
    }, TOKEN_REFRESH_INTERVAL);
  };

  const stopTokenRefreshTimer = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
      console.log('Token refresh timer stopped.');
    }
  };

  // 重置状态
  const reset = () => {
    isAuthenticated.value = false;
    user.value = null;
    loading.value = false;
  };

  return {
    // 状态
    isAuthenticated,
    user,
    loading,

    // 方法
    checkAuth,
    login,
    logout,
    reset,
    startTokenRefreshTimer,
    stopTokenRefreshTimer,
  };
});
