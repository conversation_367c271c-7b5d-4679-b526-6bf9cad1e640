import dayjs from 'dayjs';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import { createPinia } from 'pinia';
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import './style.css'; // 导入我们的全局样式
import axiosInstance from './utils/axios'; // 导入配置好的 axios 实例

const pinia = createPinia();
const app = createApp(App);
app.use(pinia);
app.use(router);
app.use(ElementPlus);

// 将 axios 实例挂载到全局属性
app.config.globalProperties.$axios = axiosInstance;

// TODO: Theme store related code commented out as it's not defined and not part of the core migration task.
// If theme management is required later, it can be re-evaluated.
// const themeStore = useThemeStore();
// themeStore.initializeTheme();
// router.beforeEach((to, from, next) => {
//   if (to.name !== 'fb-sy') {
//     themeStore.setTheme('plain');
//   }
//   next();
// });

app.mount('#app');
