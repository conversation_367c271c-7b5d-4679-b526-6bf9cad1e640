import { test, expect } from '@playwright/test';

test.describe('登录页面测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
  });

  test('页面加载正确', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/服务器监控系统/);
    
    // 检查登录表单元素
    await expect(page.locator('h1')).toContainText('服务器监控系统');
    await expect(page.locator('input[placeholder="用户名"]')).toBeVisible();
    await expect(page.locator('input[placeholder="密码"]')).toBeVisible();
    await expect(page.locator('button')).toContainText('登录');
  });

  test('表单验证', async ({ page }) => {
    // 点击登录按钮，不填写任何信息
    await page.click('button:has-text("登录")');
    
    // 检查验证错误信息
    await expect(page.locator('.el-form-item__error')).toBeVisible();
  });

  test('用户名验证', async ({ page }) => {
    // 输入过短的用户名
    await page.fill('input[placeholder="用户名"]', 'a');
    await page.fill('input[placeholder="密码"]', 'password123');
    await page.click('button:has-text("登录")');
    
    // 检查用户名长度验证
    await expect(page.locator('.el-form-item__error')).toContainText('用户名长度在 2 到 20 个字符');
  });

  test('密码验证', async ({ page }) => {
    // 输入过短的密码
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', '123');
    await page.click('button:has-text("登录")');
    
    // 检查密码长度验证
    await expect(page.locator('.el-form-item__error')).toContainText('密码长度在 6 到 50 个字符');
  });

  test('登录失败处理', async ({ page }) => {
    // 模拟登录失败的API响应
    await page.route('/api/login', async route => {
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: '用户名或密码错误' })
      });
    });

    // 填写登录信息
    await page.fill('input[placeholder="用户名"]', 'wronguser');
    await page.fill('input[placeholder="密码"]', 'wrongpass');
    await page.click('button:has-text("登录")');

    // 检查错误消息
    await expect(page.locator('.el-message--error')).toBeVisible();
  });

  test('登录成功跳转', async ({ page }) => {
    // 模拟登录成功的API响应
    await page.route('/api/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true, user: { username: 'admin' } })
      });
    });

    // 模拟服务器数据API
    await page.route('/api/servers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([])
      });
    });

    // 填写正确的登录信息
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'password123');
    await page.click('button:has-text("登录")');

    // 等待跳转到仪表板
    await expect(page).toHaveURL('/dashboard');
  });

  test('已登录用户自动跳转', async ({ page }) => {
    // 模拟已登录状态
    await page.route('/api/servers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([])
      });
    });

    // 访问登录页面
    await page.goto('/login');

    // 应该自动跳转到仪表板
    await expect(page).toHaveURL('/dashboard');
  });

  test('样式和布局检查', async ({ page }) => {
    // 检查登录容器的渐变背景
    const loginContainer = page.locator('.login-container');
    await expect(loginContainer).toBeVisible();
    
    // 检查登录表单的玻璃态效果
    const loginForm = page.locator('.login-form');
    await expect(loginForm).toBeVisible();
    
    // 检查响应式设计
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(loginForm).toBeVisible();
    
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(loginForm).toBeVisible();
  });

  test('键盘导航', async ({ page }) => {
    // 使用Tab键导航
    await page.keyboard.press('Tab');
    await expect(page.locator('input[placeholder="用户名"]')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('input[placeholder="密码"]')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('button:has-text("登录")')).toBeFocused();
  });

  test('Enter键提交表单', async ({ page }) => {
    // 模拟登录成功
    await page.route('/api/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });

    await page.route('/api/servers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([])
      });
    });

    // 填写表单并按Enter
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'password123');
    await page.keyboard.press('Enter');

    // 检查是否跳转
    await expect(page).toHaveURL('/dashboard');
  });
});
