/* 导入 Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Element Plus 样式已通过 unplugin-vue-components 自动导入 */

/* 自定义全局样式 */
:root {
  --el-color-primary: #3b82f6;
  --el-color-success: #10b981;
  --el-color-warning: #f59e0b;
  --el-color-danger: #ef4444;
  --el-color-info: #6b7280;
}

/* 服务器卡片样式 */
.server-card {
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease-in-out;
}

.server-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 状态指示器 */
.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 6px;
}

.status-online {
  background-color: #10b981;
}

.status-offline {
  background-color: #ef4444;
}

.status-warning {
  background-color: #f59e0b;
}

.status-running {
  background-color: #10b981;
}

.status-stopped {
  background-color: #ef4444;
}

.status-failed {
  background-color: #dc2626;
}

.status-unknown {
  background-color: #6b7280;
}

/* 进度条容器 */
.progress-container {
  width: 100%;
  height: 6px;
  background-color: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 登录页面样式 */
.login-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.login-form {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.input-field {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.input-field::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.input-field:focus {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  outline: none;
}

/* 服务卡片样式 */
.service-card {
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease-in-out;
}

.service-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 自定义动画 */
@keyframes spin-custom {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-custom {
  animation: spin-custom 0.5s linear;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .server-card {
    margin-bottom: 1rem;
  }
  
  .service-card {
    margin-bottom: 1rem;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 确保Element Plus组件与Tailwind兼容 */
.el-button {
  font-weight: 500;
}

.el-input__wrapper {
  transition: all 0.2s ease-in-out;
}

.el-card {
  border-radius: 8px;
}

/* 暗色模式支持（如果需要） */
@media (prefers-color-scheme: dark) {
  :root {
    --el-bg-color: #1f2937;
    --el-text-color-primary: #f9fafb;
    --el-border-color: #374151;
  }
}
