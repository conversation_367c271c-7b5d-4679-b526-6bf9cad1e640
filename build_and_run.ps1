# PowerShell 脚本：标准 go build 方案，支持多平台交叉编译，输出到 dist 目录
# 1. 检查 go 是否已安装
# 2. 创建 dist 目录
# 3. 编译 Windows x64 可执行文件
# 4. 编译 Linux x64 可执行文件
# 5. 编译 Linux ARM64 可执行文件
# 6. 输出编译结果

# 检查 go 是否安装
if (-not (Get-Command go -ErrorAction SilentlyContinue)) {
    Write-Host "[错误] 未检测到 Go，请先安装 Go 环境。" -ForegroundColor Red
    exit 1
}

# 定义路径
$projectRoot = Split-Path -Parent $MyInvocation.MyCommand.Definition
$entryFile = Join-Path $projectRoot 'main.go'
$distDir = Join-Path $projectRoot 'dist'
$winExe = Join-Path $distDir 'agent_win_amd64.exe'
$linuxExe = Join-Path $distDir 'agent_linux_amd64'
$linuxArmExe = Join-Path $distDir 'agent_linux_arm64'

# 创建 dist 目录（如不存在）
if (-not (Test-Path $distDir)) {
    New-Item -ItemType Directory -Path $distDir | Out-Null
    Write-Host "[信息] 已创建 dist 目录。"
}

# 编译 Windows x64
Write-Host "[信息] 正在编译 Windows x64..."
$env:GOOS = "windows"
$env:GOARCH = "amd64"
$env:CGO_ENABLED = "1"
go build -o $winExe main.go
if (-not (Test-Path $winExe)) {
    Write-Host "[错误] Windows x64 编译失败。" -ForegroundColor Red
    exit 1
}
Write-Host "[成功] 生成 $winExe"

# 编译 Linux x64
Write-Host "[信息] 正在编译 Linux x64..."
$env:GOOS = "linux"
$env:GOARCH = "amd64"
$env:CGO_ENABLED = "0"
go build -o $linuxExe main.go
if (-not (Test-Path $linuxExe)) {
    Write-Host "[错误] Linux x64 编译失败。" -ForegroundColor Red
    exit 1
}
Write-Host "[成功] 生成 $linuxExe"

# 编译 Linux ARM64
Write-Host "[信息] 正在编译 Linux ARM64..."
$env:GOOS = "linux"
$env:GOARCH = "arm64"
$env:CGO_ENABLED = "0"
go build -o $linuxArmExe main.go
if (-not (Test-Path $linuxArmExe)) {
    Write-Host "[错误] Linux ARM64 编译失败。" -ForegroundColor Red
    exit 1
}
Write-Host "[成功] 生成 $linuxArmExe"

Write-Host "[完成] 标准 go build 方案编译流程结束。" -ForegroundColor Green
