import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useNotificationStore = defineStore('notification', () => {
  const message = ref('');
  const type = ref('info'); // 'info', 'success', 'warning', 'error'
  const show = ref(false);
  const timeoutId = ref(null);

  const showNotification = (msg, msgType = 'info', duration = 3000) => {
    // Clear any existing timeout
    if (timeoutId.value) {
      clearTimeout(timeoutId.value);
    }

    message.value = msg;
    type.value = msgType;
    show.value = true;

    timeoutId.value = setTimeout(() => {
      hideNotification();
    }, duration);
  };

  const hideNotification = () => {
    show.value = false;
    message.value = '';
    type.value = 'info';
    if (timeoutId.value) {
      clearTimeout(timeoutId.value);
      timeoutId.value = null;
    }
  };

  return {
    message,
    type,
    show,
    showNotification,
    hideNotification,
  };
});
