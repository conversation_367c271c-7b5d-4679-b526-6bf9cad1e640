{"$schema": "https://json.schemastore.org/jsconfig", "name": "server-monitor-vue", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "powershell -ExecutionPolicy Bypass -File ./kill-port.ps1 -Port 7799 -Name Egg && vite --host", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "install:browsers": "playwright install"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "element-plus": "^2.10.4", "pinia": "^2.3.1", "vue": "^3.5.18", "vue-axios": "^3.5.2", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@playwright/test": "^1.54.1", "@rollup/plugin-strip": "^3.0.4", "@tailwindcss/postcss": "^4.1.11", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-vue": "^10.3.0", "postcss": "^8.4.47", "prettier": "^3.6.2", "rollup-plugin-visualizer": "^6.0.3", "serve": "^14.2.4", "tailwindcss": "^4.1.11", "terser": "^5.43.1", "unplugin-vue-components": "^0.27.5", "vite": "^5.4.19"}}