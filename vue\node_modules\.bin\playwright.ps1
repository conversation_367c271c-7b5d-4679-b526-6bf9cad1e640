#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="I:\agent_new\vue\node_modules\.pnpm\@playwright+test@1.54.1\node_modules\@playwright\test\node_modules;I:\agent_new\vue\node_modules\.pnpm\@playwright+test@1.54.1\node_modules\@playwright\node_modules;I:\agent_new\vue\node_modules\.pnpm\@playwright+test@1.54.1\node_modules;I:\agent_new\vue\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/i/agent_new/vue/node_modules/.pnpm/@playwright+test@1.54.1/node_modules/@playwright/test/node_modules:/mnt/i/agent_new/vue/node_modules/.pnpm/@playwright+test@1.54.1/node_modules/@playwright/node_modules:/mnt/i/agent_new/vue/node_modules/.pnpm/@playwright+test@1.54.1/node_modules:/mnt/i/agent_new/vue/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../@playwright/test/cli.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../@playwright/test/cli.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../@playwright/test/cli.js" $args
  } else {
    & "node$exe"  "$basedir/../@playwright/test/cli.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
