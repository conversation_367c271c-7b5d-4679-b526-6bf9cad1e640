<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XSS 修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .safe { background-color: #d4edda; border-color: #c3e6cb; }
        .unsafe { background-color: #f8d7da; border-color: #f5c6cb; }
        .result { margin-top: 10px; padding: 10px; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <h1>XSS 攻击防护测试</h1>
    
    <div class="test-case safe">
        <h3>✅ 安全方法 - 使用 textContent</h3>
        <p>测试恶意脚本: <code>&lt;script&gt;alert('XSS')&lt;/script&gt;</code></p>
        <div class="result" id="safe-result"></div>
    </div>
    
    <div class="test-case unsafe">
        <h3>❌ 不安全方法 - 使用 innerHTML (仅用于对比)</h3>
        <p>相同的恶意脚本通过 innerHTML 插入:</p>
        <div class="result" id="unsafe-result"></div>
    </div>

    <div class="test-case safe">
        <h3>✅ 新的安全 createServerCard 函数测试</h3>
        <p>测试包含恶意脚本的服务器数据:</p>
        <div class="result" id="server-card-container"></div>
    </div>

    <script>
        // HTML转义函数，防止XSS攻击
        function escapeHtml(text) {
            if (text == null) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 模拟新的安全 createServerCard 函数（简化版）
        function createServerCard(server) {
            const cardDiv = document.createElement('div');
            cardDiv.style.border = '1px solid #ddd';
            cardDiv.style.padding = '10px';
            cardDiv.style.margin = '10px 0';
            
            const nameH3 = document.createElement('h3');
            nameH3.textContent = server.name; // 安全设置文本内容
            
            const hostnameDiv = document.createElement('div');
            hostnameDiv.textContent = server.hostname || ''; // 安全设置文本内容
            
            const ipDiv = document.createElement('div');
            ipDiv.textContent = server.ip || ''; // 安全设置文本内容
            
            cardDiv.appendChild(nameH3);
            cardDiv.appendChild(hostnameDiv);
            cardDiv.appendChild(ipDiv);
            
            return cardDiv;
        }

        // 测试数据 - 包含恶意脚本
        const maliciousData = "<script>alert('XSS Attack!');</script>";
        const testServer = {
            name: "Server <script>alert('XSS in name!');</script>",
            hostname: "host<img src=x onerror=alert('XSS in hostname!')>",
            ip: "***********<script>alert('XSS in IP!');</script>"
        };

        // 测试1: 安全方法
        document.getElementById('safe-result').textContent = maliciousData;

        // 测试2: 不安全方法 (注释掉以避免实际执行)
        document.getElementById('unsafe-result').innerHTML = 
            '<p style="color: red;">如果使用 innerHTML，这里会执行恶意脚本: ' + 
            escapeHtml(maliciousData) + '</p>';

        // 测试3: 新的安全 createServerCard 函数
        const serverCard = createServerCard(testServer);
        document.getElementById('server-card-container').appendChild(serverCard);

        console.log('XSS 防护测试完成');
        console.log('如果没有弹出任何 alert 对话框，说明 XSS 防护有效');
    </script>
</body>
</html>
