# 数据脱敏功能说明

## 概述

为了解决第4项安全风险"敏感信息泄露"，我们实现了一套完整的数据脱敏功能，可以有效保护服务器IP地址、主机名等敏感信息。

## 功能特性

### 🔒 支持的脱敏类型

1. **IP地址脱敏**
   - IPv4地址：支持部分和完全脱敏
   - IPv6地址：支持部分和完全脱敏
   - 自动识别IP地址格式

2. **主机名脱敏**
   - 使用通用标识符替换真实主机名
   - 保留服务器ID用于管理识别

### 🎛️ 脱敏模式

| 模式 | 说明 | IPv4示例 | 主机名示例 |
|------|------|----------|------------|
| `none` | 不脱敏 | `*************` | `web-server-prod` |
| `partial` | 部分脱敏 | `192.168.1.***` | `server-1` |
| `full` | 完全脱敏 | `192.168.***.***` | `server-***` |

## 配置方法

### 1. 服务器配置文件

在 `server.json` 中添加以下配置：

```json
{
  "data_masking": {
    "enabled": true,
    "masking_mode": "partial",
    "mask_ip": true,
    "mask_hostname": false
  }
}
```

### 2. 配置参数说明

- `enabled`: 是否启用数据脱敏功能
- `masking_mode`: 脱敏模式 (`none`, `partial`, `full`)
- `mask_ip`: 是否对IP地址进行脱敏
- `mask_hostname`: 是否对主机名进行脱敏

## 实现原理

### 后端实现

1. **脱敏函数**：
   - `maskIPAddress()`: IP地址脱敏
   - `maskHostname()`: 主机名脱敏
   - `applyDataMasking()`: 应用脱敏到服务器数据

2. **API集成**：
   - 在 `getServersAPI` 函数中自动应用脱敏
   - 确保前端接收到的数据已经脱敏

### 安全优势

1. **防止信息泄露**：
   - 隐藏真实IP地址，防止网络扫描
   - 保护内部网络结构信息
   - 减少攻击面

2. **灵活配置**：
   - 支持多种脱敏级别
   - 可根据安全需求调整
   - 不影响系统功能

3. **向后兼容**：
   - 默认配置保持原有行为
   - 可选择性启用脱敏功能

## 使用示例

### 演示脱敏效果

运行演示程序查看脱敏效果：

```bash
go run demo_data_masking.go
```

### 配置示例

**高安全环境**（推荐）：
```json
{
  "data_masking": {
    "enabled": true,
    "masking_mode": "full",
    "mask_ip": true,
    "mask_hostname": true
  }
}
```

**中等安全环境**：
```json
{
  "data_masking": {
    "enabled": true,
    "masking_mode": "partial",
    "mask_ip": true,
    "mask_hostname": false
  }
}
```

**开发环境**：
```json
{
  "data_masking": {
    "enabled": false,
    "masking_mode": "none",
    "mask_ip": false,
    "mask_hostname": false
  }
}
```

## 注意事项

1. **性能影响**：脱敏处理对性能影响极小
2. **日志记录**：脱敏不影响后端日志记录
3. **管理功能**：管理员仍可通过后端访问完整信息
4. **配置更新**：修改配置后需要重启服务器

## 安全建议

1. **生产环境**建议启用 `partial` 或 `full` 模式
2. **面向公网**的系统建议使用 `full` 模式
3. **内网环境**可使用 `partial` 模式平衡安全性和可用性
4. **定期审查**脱敏配置，确保符合安全要求
