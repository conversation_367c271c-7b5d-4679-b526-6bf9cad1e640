<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>服务管理 - 服务器监控系统</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- Element Plus CDN -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/element-plus/dist/index.css"
    />
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>

    <!-- Day.js CDN -->
    <script src="https://unpkg.com/dayjs@1.10.4/dayjs.min.js"></script>
    <script src="https://unpkg.com/dayjs@1.10.4/plugin/duration.js"></script>
    <script src="https://unpkg.com/dayjs@1.10.4/plugin/relativeTime.js"></script>
    <script src="https://unpkg.com/dayjs@1.10.4/locale/zh-cn.js"></script>

    <!-- 自定义样式 -->
    <style>
      .service-card {
        border: 1px solid #e5e7eb;
      }

      .service-card:hover {
        border-color: #3b82f6;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .status-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 6px;
      }

      .status-running {
        background-color: #10b981;
      }

      .status-stopped {
        background-color: #ef4444;
      }

      .status-failed {
        background-color: #f59e0b;
      }

      .service-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
      }

      @media (min-width: 1024px) {
        .service-grid {
          grid-template-columns: repeat(3, 1fr);
        }
      }

      @media (min-width: 1280px) {
        .service-grid {
          grid-template-columns: repeat(4, 1fr);
        }
      }
    </style>
  </head>
  <body
    class="bg-gradient-to-br from-white via-gray-50 to-gray-100 min-h-screen"
  >
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <h1 class="text-xl font-bold text-gray-900">服务器监控系统</h1>
            </div>
            <div class="hidden md:ml-6 md:flex md:space-x-8">
              <a
                href="/dashboard"
                class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >仪表板</a
              >
              <a
                href="#"
                class="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >服务管理</a
              >
              <a
                href="system-example.html"
                class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium"
                >系统监控</a
              >
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-500" id="current-time"></span>
            <button
              onclick="logout()"
              class="text-gray-500 hover:text-red-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
              title="注销"
            >
              注销
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- 页面标题 -->
        <div class="mb-8">
          <div
            class="flex flex-col md:flex-row md:items-center md:justify-between"
          >
            <div>
              <h2 class="text-3xl font-bold text-gray-900">服务管理</h2>
              <p class="mt-2 text-gray-600" id="server-info">
                管理 Supervisor、Systemd、Docker 服务
              </p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-wrap gap-3">
              <!-- 搜索框 -->
              <div class="relative">
                <input
                  type="text"
                  id="service-search"
                  placeholder="搜索服务名称..."
                  class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
                  oninput="filterServices()"
                />
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <svg
                    class="h-5 w-5 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    ></path>
                  </svg>
                </div>
              </div>
              <!-- 服务类型过滤 -->
              <select
                id="service-type-filter"
                class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                onchange="filterServices()"
              >
                <option value="all">所有服务</option>
                <option value="supervisor">Supervisor</option>
                <option value="systemd">Systemd</option>
                <option value="docker">Docker</option>
              </select>
              <!-- 状态过滤 -->
              <select
                id="status-filter"
                class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                onchange="filterServices()"
              >
                <option value="all">所有状态</option>
                <option value="running">运行中</option>
                <option value="stopped">已停止</option>
                <option value="failed">失败</option>
              </select>
              <!-- 刷新按钮 -->
              <button
                onclick="refreshServices()"
                class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                <svg
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- 统计概览 -->
        <!-- 第一行：CPU和内存 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- CPU 使用率 -->
          <div class="metric-card bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">CPU 使用率</h3>
              <div
                class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-blue-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
              </div>
            </div>
            <div class="mb-2">
              <span class="text-3xl font-bold text-gray-900" id="cpu-usage"
                >加载中...</span
              >
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                class="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full progress-bar"
                id="cpu-bar"
                style="width: 0%"
              ></div>
            </div>
            <p class="text-sm text-gray-600" id="cpu-info">获取中...</p>
          </div>

          <!-- 内存使用率 -->
          <div class="metric-card bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">内存使用率</h3>
              <div
                class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-green-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                  ></path>
                </svg>
              </div>
            </div>
            <div class="mb-2">
              <span class="text-3xl font-bold text-gray-900" id="memory-usage"
                >加载中...</span
              >
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full progress-bar"
                id="memory-bar"
                style="width: 0%"
              ></div>
            </div>
            <p class="text-sm text-gray-600" id="memory-info">获取中...</p>
          </div>
        </div>

        <!-- 第二行：磁盘使用率 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- 磁盘使用率 -->
          <div class="metric-card bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">磁盘使用率</h3>
              <div
                class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-yellow-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
            </div>
            <div class="mb-2">
              <span class="text-3xl font-bold text-gray-900" id="disk-usage"
                >加载中...</span
              >
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                class="bg-gradient-to-r from-yellow-400 to-yellow-600 h-2 rounded-full progress-bar"
                id="disk-bar"
                style="width: 0%"
              ></div>
            </div>
            <p class="text-sm text-gray-600" id="disk-info">获取中...</p>
          </div>

          <!-- 网络流量 -->
          <div class="metric-card bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">网络流量</h3>
              <div
                class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-purple-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
            </div>
            <div class="mb-2">
              <div class="flex justify-between text-sm">
                <span class="text-gray-600"
                  >↑ <span id="upload-speed">获取中...</span></span
                >
                <span class="text-gray-600"
                  >↓ <span id="download-speed">获取中...</span></span
                >
              </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                class="bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full progress-bar"
                id="network-bar"
                style="width: 0%"
              ></div>
            </div>
            <p class="text-sm text-gray-600" id="network-info">获取中...</p>
          </div>
        </div>

        <!-- 服务管理标签页 -->
        <div class="bg-white rounded-lg shadow-md">
          <!-- 标签页头部 -->
          <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              <button
                onclick="switchTab('supervisor')"
                class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                data-tab="supervisor"
              >
                Supervisor 服务
              </button>
              <button
                onclick="switchTab('systemd')"
                class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                data-tab="systemd"
              >
                Systemd 服务
              </button>
              <button
                onclick="switchTab('docker')"
                class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                data-tab="docker"
              >
                Docker 容器
              </button>
            </nav>
          </div>

          <!-- 标签页内容 -->
          <div class="p-6">
            <!-- Supervisor 服务 -->
            <div id="supervisor-tab" class="tab-content">
              <div
                class="grid grid-cols-1 lg:grid-cols-2 gap-6"
                id="supervisor-services"
              >
                <!-- 服务卡片将通过JavaScript动态生成 -->
              </div>
            </div>

            <!-- Systemd 服务 -->
            <div id="systemd-tab" class="tab-content hidden">
              <div
                class="grid grid-cols-1 lg:grid-cols-2 gap-6"
                id="systemd-services"
              >
                <!-- 服务卡片将通过JavaScript动态生成 -->
              </div>
            </div>

            <!-- Docker 容器 -->
            <div id="docker-tab" class="tab-content hidden">
              <div
                class="grid grid-cols-1 lg:grid-cols-2 gap-6"
                id="docker-services"
              >
                <!-- 服务卡片将通过JavaScript动态生成 -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <script>
      dayjs.extend(dayjs_plugin_duration);
      dayjs.extend(dayjs_plugin_relativeTime);
      dayjs.locale('zh-cn');

      let currentTab = 'supervisor';
      let currentServices = {
        supervisor: [],
        systemd: [],
        docker: [],
      };

      // 格式化运行时间（秒）为可读字符串
      function formatUptime(seconds) {
        if (typeof seconds !== 'number' || seconds < 0) {
          return '--';
        }

        const duration = dayjs.duration(seconds, 'seconds');

        // Less than a minute
        if (seconds < 60) {
          return `${seconds} 秒`;
        }
        // Less than an hour
        else if (seconds < 3600) {
          return `${duration.minutes()} 分钟`;
        }
        // Less than a day
        else if (seconds < 86400) {
          return `${duration.hours()} 小时 ${duration.minutes()} 分钟`;
        }
        // More than a day
        else {
          return `${duration.days()} 天 ${duration.hours()} 小时 ${duration.minutes()} 分钟`;
        }
      }

      // 检查认证状态
      function checkAuth() {
        // 使用httpOnly cookie认证，通过API请求验证
        return true; // 让API请求处理认证检查
      }

      // 获取认证头
      function getAuthHeaders() {
        // 使用httpOnly cookie认证，不需要额外的Authorization header
        return {
          'Content-Type': 'application/json',
        };
      }

      // 处理认证错误
      function handleAuthError(response) {
        if (response.status === 401) {
          // 认证失败，重定向到登录页
          window.location.href = '/login';
          return true;
        }
        return false;
      }

      // 注销函数
      function logout() {
        // 清除认证状态，重定向到登录页
        window.location.href = '/login';
      }

      // 生成服务卡片HTML
      function createServiceCard(service, type) {
        const statusClass =
          service.status === 'running'
            ? 'status-running'
            : service.status === 'stopped'
            ? 'status-stopped'
            : service.status === 'failed'
            ? 'status-failed'
            : 'status-unknown'; // Add unknown status class

        const statusText =
          service.status === 'running'
            ? '运行中'
            : service.status === 'stopped'
            ? '已停止'
            : service.status === 'failed'
            ? '失败'
            : '未知'; // Add unknown status text

        const statusTextClass =
          service.status === 'running'
            ? 'text-green-600'
            : service.status === 'stopped'
            ? 'text-red-600'
            : service.status === 'failed'
            ? 'text-yellow-600'
            : 'text-gray-600'; // Add unknown status text class

        const actionButtons =
          service.status === 'running'
            ? `<button onclick="stopService('${service.name}', '${type}')" class="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors">停止</button>
                 <button onclick="restartService('${service.name}', '${type}')" class="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 transition-colors">重启</button>`
            : `<button onclick="startService('${service.name}', '${type}')" class="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600 transition-colors">启动</button>`;

        return `
                <div class="service-card bg-gray-50 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-all duration-300">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <span class="status-indicator ${statusClass}"></span>
                            <h3 class="text-lg font-semibold text-gray-900">${
                              service.name
                            }</h3>
                        </div>
                        <span class="text-sm font-medium ${statusTextClass}">${statusText}</span>
                    </div>

                    <p class="text-sm text-gray-600 mb-3">${
                      service.description || '无描述'
                    }</p>

                    <div class="grid grid-cols-2 gap-4 mb-3 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">PID:</span>
                            <span class="text-gray-600">${
                              service.pid || '--'
                            }</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">运行时间:</span>
                            <span class="text-gray-600">${formatUptime(
                              service.uptime
                            )}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">内存:</span>
                            <span class="text-gray-600">${
                              service.memory || '--'
                            }</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">CPU:</span>
                            <span class="text-gray-600">${
                              service.cpu || '--'
                            }</span>
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        ${actionButtons}
                        <button onclick="viewLogs('${
                          service.name
                        }', '${type}')" class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors">日志</button>
                    </div>
                </div>
            `;
      }

      // 渲染服务列表
      function renderServices(type = currentTab) {
        const container = document.getElementById(`${type}-services`);
        const services = getFilteredServices(type);

        if (services.length === 0) {
          const allServices = currentServices[type] || [];
          const message =
            allServices.length === 0
              ? `没有找到 ${type} 服务。`
              : '没有符合筛选条件的服务。';
          container.innerHTML = `<div class="col-span-full text-center py-12 text-gray-500">${message}</div>`;
        } else {
          container.innerHTML = services
            .map(service => createServiceCard(service, type))
            .join('');
        }

        // 更新服务计数
        updateServiceCount(
          type,
          services.length,
          (currentServices[type] || []).length
        );
      }

      // 获取过滤后的服务列表
      function getFilteredServices(type) {
        const services = currentServices[type] || [];
        const searchTerm = document
          .getElementById('service-search')
          .value.toLowerCase();
        const serviceTypeFilter = document.getElementById(
          'service-type-filter'
        ).value;
        const statusFilter = document.getElementById('status-filter').value;

        return services.filter(service => {
          // 搜索过滤
          const matchesSearch = service.name.toLowerCase().includes(searchTerm);

          // 服务类型过滤
          const matchesType =
            serviceTypeFilter === 'all' || serviceTypeFilter === type;

          // 状态过滤
          const matchesStatus =
            statusFilter === 'all' ||
            (statusFilter === 'running' &&
              (service.status === 'running' || service.status === 'active')) ||
            (statusFilter === 'stopped' &&
              (service.status === 'stopped' ||
                service.status === 'inactive')) ||
            (statusFilter === 'failed' &&
              (service.status === 'failed' || service.status === 'error'));

          return matchesSearch && matchesType && matchesStatus;
        });
      }

      // 过滤服务（搜索和过滤器变化时调用）
      function filterServices() {
        renderServices(currentTab);
      }

      // 更新服务计数显示
      function updateServiceCount(type, filteredCount, totalCount) {
        // 可以在这里添加服务计数显示逻辑
        console.log(`${type}: ${filteredCount}/${totalCount} services`);
      }

      // 切换标签页
      async function switchTab(tab) {
        // 隐藏所有标签页内容
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.add('hidden');
        });

        // 重置所有标签按钮样式
        document.querySelectorAll('.tab-button').forEach(button => {
          button.classList.remove('border-blue-500', 'text-blue-600');
          button.classList.add('border-transparent', 'text-gray-500');
        });

        // 显示选中的标签页
        document.getElementById(`${tab}-tab`).classList.remove('hidden');

        // 激活选中的标签按钮
        const activeButton = document.querySelector(`[data-tab="${tab}"]`);
        activeButton.classList.remove('border-transparent', 'text-gray-500');
        activeButton.classList.add('border-blue-500', 'text-blue-600');

        currentTab = tab;
        await fetchServices(tab); // Fetch services for the new tab
        renderServices(tab);
      }

      // 服务操作函数
      async function startService(name, type) {
        const serverId = window.currentServerId || 'default';
        const serverName = window.currentServerName || 'Unknown Server';

        try {
          console.log(
            `Starting ${type} service: ${name} on server ${serverName} (ID: ${serverId})`
          );

          const response = await fetch(`/api/services/${type}/start`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include', // 包含httpOnly cookie
            body: JSON.stringify({
              serverId: parseInt(serverId),
              serviceName: name,
              serviceType: type,
            }),
          });

          if (response.ok) {
            const result = await response.json();
            alert(`成功启动 ${type} 服务: ${name}
服务器: ${serverName}`);
            await fetchServices(type); // Refresh services for current type
            renderServices(type);
          } else {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
          }
        } catch (error) {
          console.error('Error starting service:', error);
          alert(`启动服务失败: ${error.message}`);
        }
      }

      async function stopService(name, type) {
        const serverId = window.currentServerId || 'default';
        const serverName = window.currentServerName || 'Unknown Server';

        try {
          console.log(
            `Stopping ${type} service: ${name} on server ${serverName} (ID: ${serverId})`
          );

          const response = await fetch(`/api/services/${type}/stop`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include', // 包含httpOnly cookie
            body: JSON.stringify({
              serverId: parseInt(serverId),
              serviceName: name,
              serviceType: type,
            }),
          });

          if (response.ok) {
            const result = await response.json();
            alert(`成功停止 ${type} 服务: ${name}
服务器: ${serverName}`);
            await fetchServices(type);
            renderServices(type);
          } else {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
          }
        } catch (error) {
          console.error('Error stopping service:', error);
          alert(`停止服务失败: ${error.message}`);
        }
      }

      async function restartService(name, type) {
        const serverId = window.currentServerId || 'default';
        const serverName = window.currentServerName || 'Unknown Server';

        try {
          console.log(
            `Restarting ${type} service: ${name} on server ${serverName} (ID: ${serverId})`
          );

          const response = await fetch(`/api/services/${type}/restart`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include', // 包含httpOnly cookie
            body: JSON.stringify({
              serverId: parseInt(serverId),
              serviceName: name,
              serviceType: type,
            }),
          });

          if (response.ok) {
            const result = await response.json();
            alert(`成功重启 ${type} 服务: ${name}
服务器: ${serverName}`);
            await fetchServices(type);
            renderServices(type);
          } else {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
          }
        } catch (error) {
          console.error('Error restarting service:', error);
          alert(`重启服务失败: ${error.message}`);
        }
      }

      async function viewLogs(name, type) {
        const token = checkAuth();
        if (!token) return;

        const serverId = window.currentServerId || 'default';
        const serverName = window.currentServerName || 'Unknown Server';

        try {
          console.log(
            `Viewing logs for ${type} service: ${name} on server ${serverName} (ID: ${serverId})`
          );

          const response = await fetch(
            `/api/services/logs?serverId=${serverId}&serviceName=${name}&serviceType=${type}`,
            {
              headers: {
                'Content-Type': 'application/json',
              },
              credentials: 'include', // 包含httpOnly cookie
            }
          );

          if (handleAuthError(response)) return;

          if (response.ok) {
            const logs = await response.text();
            const logWindow = window.open('', '_blank', 'width=800,height=600');
            logWindow.document.write(`
              <html>
                <head>
                  <title>${type} 服务日志 - ${name} (${serverName})</title>
                  <style>
                    body { font-family: monospace; padding: 20px; background: #1a1a1a; color: #00ff00; }
                    pre { white-space: pre-wrap; word-wrap: break-word; }
                  </style>
                </head>
                <body>
                  <h3>${type} 服务日志 - ${name}</h3>
                  <p>服务器: ${serverName} (ID: ${serverId})</p>
                  <hr>
                  <pre>${logs}</pre>
                </body>
              </html>
            `);
          } else {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
          }
        } catch (error) {
          console.error('Error viewing logs:', error);
          alert(`查看日志失败: ${error.message}`);
        }
      }

      async function fetchServices(type) {
        const token = checkAuth();
        if (!token) return;

        const serverId = window.currentServerId;
        if (!serverId) {
          console.warn('Server ID not found. Cannot fetch services.');
          currentServices[type] = [];
          return;
        }

        try {
          const response = await fetch(
            `/api/services/list?serverId=${serverId}&serviceType=${type}`,
            {
              headers: {
                'Content-Type': 'application/json',
              },
              credentials: 'include', // 包含httpOnly cookie
            }
          );

          if (handleAuthError(response)) return;

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const services = await response.json();
          currentServices[type] = services;
          console.log(`Fetched ${type} services:`, services);
        } catch (error) {
          console.error(`Error fetching ${type} services:`, error);
          currentServices[type] = [];
        }
      }

      async function refreshServices() {
        const button = event.target.closest('button');
        const svg = button.querySelector('svg');
        svg.classList.add('animate-spin-custom');

        await fetchServices(currentTab);
        renderServices(currentTab);

        setTimeout(() => {
          svg.classList.remove('animate-spin-custom');
        }, 500); // Shorter timeout for visual feedback
      }

      // 更新时间
      function updateTime() {
        document.getElementById('current-time').textContent =
          new Date().toLocaleString();
      }

      // 获取URL参数
      function getUrlParameter(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
      }

      // WebSocket connection for real-time data
      let ws = null;
      let wsReconnectAttempts = 0;
      const maxReconnectAttempts = 5;

      function initWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/frontend`;

        ws = new WebSocket(wsUrl);

        ws.onopen = function () {
          console.log('WebSocket connected');
          wsReconnectAttempts = 0;

          // WebSocket认证现在依赖httpOnly cookie
          // 服务器会从cookie中读取认证信息
          ws.send(
            JSON.stringify({
              type: 'auth',
              timestamp: new Date().toISOString(),
            })
          );
        };

        ws.onmessage = function (event) {
          try {
            const message = JSON.parse(event.data);
            handleWebSocketMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        ws.onclose = function () {
          console.log('WebSocket disconnected');
          // Attempt to reconnect
          if (wsReconnectAttempts < maxReconnectAttempts) {
            wsReconnectAttempts++;
            console.log(
              `Attempting to reconnect (${wsReconnectAttempts}/${maxReconnectAttempts})...`
            );
            setTimeout(initWebSocket, 3000);
          } else {
            console.log(
              'Max reconnection attempts reached, falling back to HTTP polling'
            );
            startHttpPolling();
          }
        };

        ws.onerror = function (error) {
          console.error('WebSocket error:', error);
        };
      }

      function handleWebSocketMessage(message) {
        switch (message.type) {
          case 'auth_response':
            if (message.error) {
              console.error('WebSocket auth failed:', message.error);
              // Fallback to HTTP polling
              startHttpPolling();
            } else {
              console.log('WebSocket authenticated:', message.data);
              // Subscribe to current server
              const serverId = getUrlParameter('mid') || 1;
              ws.send(
                JSON.stringify({
                  type: 'subscribe',
                  server_id: parseInt(serverId),
                  timestamp: new Date().toISOString(),
                })
              );
            }
            break;

          case 'subscribe_response':
            console.log('Subscribed to server:', message.server_id);
            break;

          case 'system_stats_broadcast':
            updateSystemStats(message.data);
            break;

          case 'pong':
            // Handle ping response
            break;

          default:
            console.log('Unknown WebSocket message type:', message.type);
        }
      }

      // Fallback HTTP polling function
      async function fetchSystemStats() {
        try {
          // Get current server ID from URL parameters
          const mid = getUrlParameter('mid') || '1';

          const response = await fetch(`/api/system/stats?serverId=${mid}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include', // 包含httpOnly cookie
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const stats = await response.json();
          updateSystemStats(stats);
        } catch (error) {
          console.error('Failed to fetch system stats:', error);
          // 显示错误状态
          document.getElementById('cpu-usage').textContent = '获取失败';
          document.getElementById('memory-usage').textContent = '获取失败';
          document.getElementById('disk-usage').textContent = '获取失败';
          document.getElementById('upload-speed').textContent = '获取失败';
          document.getElementById('download-speed').textContent = '获取失败';
        }
      }

      function startHttpPolling() {
        console.log('Starting HTTP polling fallback');
        // 每10秒获取系统统计数据
        setInterval(fetchSystemStats, 10000);
      }

      // 更新系统统计数据显示
      function updateSystemStats(stats) {
        // 更新CPU使用率
        const cpuPercent = stats.CPU.toFixed(2);
        document.getElementById('cpu-usage').textContent = `${cpuPercent}%`;
        document.getElementById('cpu-bar').style.width = `${cpuPercent}%`;
        document.getElementById('cpu-info').textContent = '实时数据';

        // 更新内存使用率
        const memPercent = ((stats.MemUsed / stats.MemTotal) * 100).toFixed(2);
        const memUsedGB = (stats.MemUsed / 1024 / 1024 / 1024).toFixed(1);
        const memTotalGB = (stats.MemTotal / 1024 / 1024 / 1024).toFixed(1);
        document.getElementById('memory-usage').textContent = `${memPercent}%`;
        document.getElementById('memory-bar').style.width = `${memPercent}%`;
        document.getElementById(
          'memory-info'
        ).textContent = `${memUsedGB}GB / ${memTotalGB}GB`;

        // 更新磁盘使用率
        let diskPercent = 0;
        let diskUsedGB = 0;
        let diskTotalGB = 0;

        if (stats.DiskTotal > 0) {
          diskPercent = ((stats.DiskUsed / stats.DiskTotal) * 100).toFixed(2);
          diskUsedGB = (stats.DiskUsed / 1024 / 1024 / 1024).toFixed(0);
          diskTotalGB = (stats.DiskTotal / 1024 / 1024 / 1024).toFixed(0);
        } else {
          // If DiskTotal is 0, display N/A or 0%
          diskPercent = '0.00';
          diskUsedGB = '0';
          diskTotalGB = '0';
        }

        document.getElementById('disk-usage').textContent = `${diskPercent}%`;
        document.getElementById('disk-bar').style.width = `${diskPercent}%`;
        document.getElementById(
          'disk-info'
        ).textContent = `${diskUsedGB}GB / ${diskTotalGB}GB`;

        // 更新网络流量（显示累计传输量）
        const netInMB = (stats.NetInTransfer / 1024 / 1024).toFixed(1);
        const netOutMB = (stats.NetOutTransfer / 1024 / 1024).toFixed(1);
        document.getElementById('upload-speed').textContent = `${netOutMB} MB`;
        document.getElementById('download-speed').textContent = `${netInMB} MB`;
        document.getElementById('network-info').textContent = '累计传输量';

        // 网络使用率显示为一个相对值（这里简单设置为30%作为示例）
        document.getElementById('network-bar').style.width = '30%';
      }

      // 初始化服务器信息
      function initializeServerInfo() {
        const mid = getUrlParameter('mid');
        const serverName = getUrlParameter('server');

        if (mid && serverName) {
          document.getElementById(
            'server-info'
          ).innerHTML = `正在管理服务器: <strong>${decodeURIComponent(
            serverName
          )}</strong> (ID: ${mid})`;
          document.title = `服务管理 - ${decodeURIComponent(
            serverName
          )} - 服务器监控系统`;

          // 存储当前服务器信息供后续使用
          window.currentServerId = mid;
          window.currentServerName = decodeURIComponent(serverName);

          console.log(
            `Managing services for server: ${serverName} (ID: ${mid})`
          );
        } else {
          document.getElementById(
            'server-info'
          ).innerHTML = `请从仪表板选择服务器以管理服务。`;
          console.log('No server specified, showing empty services list.');
          window.currentServerId = null; // Ensure serverId is null if not provided
        }
      }

      // 页面初始化
      document.addEventListener('DOMContentLoaded', async function () {
        console.log('Services management page loaded');

        // 首先检查认证状态
        if (!checkAuth()) {
          return; // 如果未认证，会自动跳转到登录页
        }

        // 初始化服务器信息
        initializeServerInfo();

        // 初始化WebSocket连接进行实时数据更新
        initWebSocket();

        // 默认显示supervisor标签并加载服务
        await switchTab('supervisor');
        updateTime();

        // 每秒更新时间
        setInterval(updateTime, 1000);

        // 每30秒刷新当前标签页的服务列表
        setInterval(async () => {
          await fetchServices(currentTab);
          renderServices(currentTab);
        }, 30000);
      });
    </script>
  </body>
</html>
