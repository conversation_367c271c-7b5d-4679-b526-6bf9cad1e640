# test_three_client.ps1

# 定义 Go 服务端命令
$GoServerCommand = "go"
$GoServerArgs = "run main.go -s"

# 定义 Go 客户端命令
$GoClientCommand = "go"
$GoClientArgs = "run main.go -c"

# 定义 Vue 开发服务器命令
$VueDevCommand = "pnpm"
$VueDevArgs = "--prefix vue dev"
$VueDevWorkingDirectory = Join-Path $PSScriptRoot "vue"

# 存储启动的进程对象
$processes = @()

# 函数：启动进程并添加到列表中
function Start-BackgroundProcess {
    param (
        [string]$FilePath,
        [string]$Arguments,
        [string]$WorkingDirectory = $PSScriptRoot,
        [string]$Description = "进程"
    )
    Write-Host "正在启动 $Description..." -ForegroundColor Green
    try {
        $process = Start-Process -FilePath $FilePath -ArgumentList $Arguments -WorkingDirectory $WorkingDirectory -NoNewWindow -PassThru -ErrorAction Stop
        $processes += $process
        Write-Host "$Description 已启动，PID: $($process.Id)" -ForegroundColor Green
    } catch {
        Write-Error "启动 $Description 失败: $($_.Exception.Message)"
    }
}

# 函数：清理所有启动的进程
# 函数：清理所有启动的进程 (已移除，逻辑已内联到 Register-EngineEvent)

# 注册脚本退出时的清理函数
Register-EngineEvent -SourceIdentifier ([System.AppDomain]::CurrentDomain.Id.ToString() + "Exiting") -Action {
    Write-Host "正在停止所有启动的进程..." -ForegroundColor Yellow
    foreach ($p in $script:processes) { # 使用 $script:processes 访问脚本作用域变量
        if (-not $p.HasExited) {
            try {
                $p.Kill() | Out-Null
                Write-Host "已停止进程 $($p.Id) ($($p.ProcessName))" -ForegroundColor Yellow
            } catch {
                Write-Warning "停止进程 $($p.Id) ($($p.ProcessName)) 失败: $($_.Exception.Message)"
            }
        }
    }
    $script:processes = @() # 清空进程列表
    Write-Host "所有进程已停止。" -ForegroundColor Yellow
} -SupportEvent

# 启动 Go 服务端
Start-BackgroundProcess -FilePath $GoServerCommand -Arguments $GoServerArgs -Description "Go 服务端"

# 启动 Go 客户端
Start-BackgroundProcess -FilePath $GoClientCommand -Arguments $GoClientArgs -Description "Go 客户端"

# 启动 Vue 开发服务器
Start-BackgroundProcess -FilePath $VueDevCommand -Arguments $VueDevArgs -WorkingDirectory $VueDevWorkingDirectory -Description "Vue 开发服务器"

Write-Host "所有进程已在后台启动。按 Ctrl+C 停止所有进程并退出脚本。" -ForegroundColor Green

# 保持脚本运行，直到用户按下 Ctrl+C
while ($true) {
    Start-Sleep -Seconds 1
}
