# test_three_client.ps1 - 三端同时运行脚本
# 同时启动Go服务端、Go客户端和Vue前端开发服务器

param(
    [int]$ServerPort = 7788,
    [int]$VuePort = 7799,
    [int]$Delay = 3,
    [switch]$SkipPortCheck,
    [switch]$Help
)

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 显示帮助信息
function Show-Help {
    Write-ColorOutput "三端同时运行脚本" "Cyan"
    Write-ColorOutput "用法:" "Yellow"
    Write-ColorOutput "  .\test_three_client.ps1                    # 使用默认端口启动" "White"
    Write-ColorOutput "  .\test_three_client.ps1 -ServerPort 8080  # 指定服务端端口" "White"
    Write-ColorOutput "  .\test_three_client.ps1 -VuePort 3000     # 指定Vue端口" "White"
    Write-ColorOutput "  .\test_three_client.ps1 -Delay 5          # 设置启动延迟(秒)" "White"
    Write-ColorOutput "  .\test_three_client.ps1 -SkipPortCheck    # 跳过端口检查" "White"
    Write-ColorOutput "  .\test_three_client.ps1 -Help             # 显示此帮助" "White"
    Write-ColorOutput ""
    Write-ColorOutput "默认端口:" "Yellow"
    Write-ColorOutput "  - Go服务端: 7788" "Gray"
    Write-ColorOutput "  - Vue前端: 7799" "Gray"
    exit 0
}

if ($Help) {
    Show-Help
}

# 定义命令和参数
$GoServerCommand = "go"
$GoServerArgs = "run main.go -s"

$GoClientCommand = "go"
$GoClientArgs = "run main.go -c"

$VueDevCommand = "pnpm"
$VueDevArgs = "dev"
$VueDevWorkingDirectory = Join-Path $PSScriptRoot "vue"

# 存储启动的进程对象
$script:processes = @()
$script:startTime = Get-Date

# 检查端口是否被占用
function Test-Port {
    param([int]$Port)
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port $Port -InformationLevel Quiet -WarningAction SilentlyContinue
        return $connection
    } catch {
        return $false
    }
} # Missing closing brace for Test-Port function

# 检查必要的工具是否安装
function Test-Prerequisites {
    $missing = @()

    # 检查Go
    try {
        $null = go version 2>$null
        Write-ColorOutput "✓ Go已安装" "Green"
    } catch {
        $missing += "Go"
    }

    # 检查pnpm
    try {
        $null = pnpm --version 2>$null
        Write-ColorOutput "✓ pnpm已安装" "Green"
    } catch {
        $missing += "pnpm"
    }

    if ($missing.Count -gt 0) {
        Write-ColorOutput "✗ 缺少必要工具: $($missing -join ', ')" "Red"
        return $false
    }

    return $true
}

# 启动进程并添加到列表中
function Start-BackgroundProcess {
    param (
        [string]$FilePath,
        [string]$Arguments,
        [string]$WorkingDirectory = $PSScriptRoot,
        [string]$Description = "进程",
        [int]$ExpectedPort = 0
    )

    Write-ColorOutput "正在启动 $Description..." "Yellow"

    try {
        $processStartInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processStartInfo.FileName = $FilePath
        $processStartInfo.Arguments = $Arguments
        $processStartInfo.WorkingDirectory = $WorkingDirectory
        $processStartInfo.UseShellExecute = $false
        $processStartInfo.RedirectStandardOutput = $true
        $processStartInfo.RedirectStandardError = $true
        $processStartInfo.CreateNoWindow = $true

        $process = New-Object System.Diagnostics.Process
        $process.StartInfo = $processStartInfo

        # 添加输出处理
        $process.add_OutputDataReceived({
            param($proc, $e)
            if ($e.Data) {
                Write-ColorOutput "[$Description] $($e.Data)" "Gray"
            }
        })

        $process.add_ErrorDataReceived({
            param($proc, $e)
            if ($e.Data) {
                Write-ColorOutput "[$Description] ERROR: $($e.Data)" "Red"
            }
        })

        $process.Start() | Out-Null
        $process.BeginOutputReadLine()
        $process.BeginErrorReadLine()

        $script:processes += $process
        Write-ColorOutput "✓ $Description 已启动，PID: $($process.Id)" "Green"

        # 如果指定了端口，等待端口可用
        if ($ExpectedPort -gt 0) {
            Write-ColorOutput "等待端口 $ExpectedPort 可用..." "Yellow"
            $timeout = 30
            $elapsed = 0
            while ($elapsed -lt $timeout) {
                if (Test-Port -Port $ExpectedPort) {
                    Write-ColorOutput "✓ 端口 $ExpectedPort 已可用" "Green"
                    break
                }
                Start-Sleep -Seconds 1
                $elapsed++
            }
            if ($elapsed -ge $timeout) {
                Write-ColorOutput "⚠ 端口 $ExpectedPort 在 $timeout 秒内未响应" "Yellow"
            }
        }

        return $true
    } catch {
        Write-ColorOutput "✗ 启动 $Description 失败: $($_.Exception.Message)" "Red"
        return $false
    }
}

# 函数：清理所有启动的进程
# 清理所有启动的进程
function Stop-AllProcesses {
    Write-ColorOutput "正在停止所有启动的进程..." "Yellow"
    foreach ($p in $script:processes) {
        if ($p -and -not $p.HasExited) {
            try {
                $p.Kill()
                $p.WaitForExit(5000) # 等待5秒
                Write-ColorOutput "✓ 已停止进程 $($p.Id)" "Green"
            } catch {
                Write-ColorOutput "⚠ 停止进程 $($p.Id) 失败: $($_.Exception.Message)" "Yellow"
            }
        }
    }
    $script:processes = @()
    Write-ColorOutput "所有进程已停止" "Green"
}

# 注册脚本退出时的清理函数
$null = Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action {
    Stop-AllProcesses
}

# 注册Ctrl+C处理
[Console]::TreatControlCAsInput = $false
$null = Register-EngineEvent -SourceIdentifier "PowerShell.Exiting" -Action {
    Stop-AllProcesses
}

# 主函数
function Main {
    Write-ColorOutput "=== 三端同时运行脚本 ===" "Cyan"
    Write-ColorOutput "启动时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Gray"
    Write-ColorOutput ""

    # 检查必要工具
    if (-not (Test-Prerequisites)) {
        Write-ColorOutput "请安装缺少的工具后重试" "Red"
        exit 1
    }

    # 端口检查
    if (-not $SkipPortCheck) {
        Write-ColorOutput "检查端口占用情况..." "Yellow"

        if (Test-Port -Port $ServerPort) {
            Write-ColorOutput "⚠ 端口 $ServerPort 已被占用，服务端可能无法启动" "Yellow"
        }

        if (Test-Port -Port $VuePort) {
            Write-ColorOutput "⚠ 端口 $VuePort 已被占用，Vue开发服务器可能无法启动" "Yellow"
        }
    }

    Write-ColorOutput ""
    Write-ColorOutput "开始启动三个服务..." "Cyan"

    # 启动Go服务端
    if (-not (Start-BackgroundProcess -FilePath $GoServerCommand -Arguments $GoServerArgs -Description "Go服务端" -ExpectedPort $ServerPort)) {
        Write-ColorOutput "Go服务端启动失败，退出" "Red"
        Stop-AllProcesses
        exit 1
    }

    # 等待服务端启动
    Write-ColorOutput "等待 $Delay 秒，确保服务端启动完成..." "Yellow"
    Start-Sleep -Seconds $Delay

    # 启动Go客户端
    if (-not (Start-BackgroundProcess -FilePath $GoClientCommand -Arguments $GoClientArgs -Description "Go客户端")) {
        Write-ColorOutput "Go客户端启动失败" "Red"
    }

    # 启动Vue开发服务器
    if (-not (Start-BackgroundProcess -FilePath $VueDevCommand -Arguments $VueDevArgs -WorkingDirectory $VueDevWorkingDirectory -Description "Vue开发服务器" -ExpectedPort $VuePort)) {
        Write-ColorOutput "Vue开发服务器启动失败" "Red"
    }

    Write-ColorOutput ""
    Write-ColorOutput "=== 所有服务已启动 ===" "Green"
    Write-ColorOutput "Go服务端: http://localhost:$ServerPort" "Cyan"
    Write-ColorOutput "Vue前端: http://localhost:$VuePort" "Cyan"
    Write-ColorOutput ""
    Write-ColorOutput "按 Ctrl+C 或任意键停止所有服务..." "Yellow"

    # 等待用户输入
    try {
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    } catch {
        # 处理Ctrl+C等中断
        Write-ColorOutput "收到中断信号" "Yellow"
    }

    # 停止所有进程
    Stop-AllProcesses

    $endTime = Get-Date
    $duration = $endTime - $script:startTime
    Write-ColorOutput ""
    Write-ColorOutput "运行时长: $($duration.ToString('hh\:mm\:ss'))" "Gray"
    Write-ColorOutput "所有服务已停止" "Green"
}

# 运行主函数
try {
    Main
} catch {
    Write-ColorOutput "发生错误: $($_.Exception.Message)" "Red"
    Stop-AllProcesses
    exit 1
} finally {
    # 确保清理
    Stop-AllProcesses
}
