# JWT密钥配置说明

## 问题描述

当运行服务器时出现以下错误：
```
JWT_SECRET environment variable not set. Please set it for security.
```

## 解决方案

现在有三种方式设置JWT密钥：

### 方法1：使用命令行参数 `-key`（推荐）

```bash
# 直接在命令行中指定JWT密钥
./agent_linux_amd64 -s -f server.json -key "your_secure_random_jwt_secret_key_here"
```

### 方法2：设置环境变量

```bash
# 设置环境变量
export JWT_SECRET="your_secure_random_jwt_secret_key_here"

# 然后运行程序
./agent_linux_amd64 -s -f server.json
```

### 方法3：使用默认密钥（仅用于测试）

```bash
# 不设置任何密钥，程序会使用默认密钥并显示警告
./agent_linux_amd64 -s -f server.json
```

## 安全建议

1. **生产环境**：必须使用强随机密钥，至少32个字符
2. **密钥生成**：可以使用以下命令生成安全的密钥：
   ```bash
   # 生成32字节的随机密钥
   openssl rand -base64 32
   
   # 或者使用其他方法
   head -c 32 /dev/urandom | base64
   ```

3. **密钥示例**：
   ```bash
   # 好的密钥示例
   ./agent_linux_amd64 -s -f server.json -key "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="
   ```

## 优先级

程序按以下优先级选择JWT密钥：
1. 命令行参数 `-key`（最高优先级）
2. 环境变量 `JWT_SECRET`
3. 默认密钥（最低优先级，仅用于开发/测试）

## 使用示例

```bash
# 在线上服务器运行
./agent_linux_amd64 -s -f server.json -key "your_production_jwt_secret_key"

# 本地开发测试（会显示警告）
./agent_linux_amd64 -s -f server.json

# 查看帮助信息
./agent_linux_amd64 -h
```

## 注意事项

- JWT密钥用于生成和验证用户登录令牌
- 密钥泄露会导致安全风险
- 更换密钥会使所有现有登录令牌失效
- 建议定期更换生产环境的JWT密钥
