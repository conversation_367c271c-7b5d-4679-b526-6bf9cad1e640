package main

import (
	"fmt"
	"strings"
)

// maskIPAddress 对IP地址进行脱敏处理
func maskIPAddress(ip string, maskingMode string) string {
	if ip == "" || maskingMode == "none" {
		return ip
	}

	// 解析IP地址
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		// 如果不是IPv4地址，尝试处理IPv6
		if strings.Contains(ip, ":") {
			return maskIPv6Address(ip, maskingMode)
		}
		return ip // 无法识别的格式，返回原值
	}

	switch maskingMode {
	case "partial":
		// 部分脱敏：隐藏最后一个八位组
		return fmt.Sprintf("%s.%s.%s.***", parts[0], parts[1], parts[2])
	case "full":
		// 完全脱敏：只显示网络段
		return fmt.Sprintf("%s.%s.***.***", parts[0], parts[1])
	default:
		return ip
	}
}

// maskIPv6Address 对IPv6地址进行脱敏处理
func maskIPv6Address(ip string, maskingMode string) string {
	if ip == "" || maskingMode == "none" {
		return ip
	}

	// 简单的IPv6脱敏：隐藏后半部分
	parts := strings.Split(ip, ":")
	if len(parts) < 4 {
		return ip // 格式不正确，返回原值
	}

	switch maskingMode {
	case "partial":
		// 部分脱敏：隐藏后4段
		if len(parts) >= 4 {
			return strings.Join(parts[:4], ":") + ":****:****:****:****"
		}
	case "full":
		// 完全脱敏：只显示前2段
		if len(parts) >= 2 {
			return strings.Join(parts[:2], ":") + ":****:****:****:****:****:****"
		}
	}
	return ip
}

// maskHostname 对主机名进行脱敏处理
func maskHostname(hostname string, serverID int, maskingMode string) string {
	if hostname == "" || maskingMode == "none" {
		return hostname
	}

	switch maskingMode {
	case "partial":
		// 部分脱敏：使用服务器ID生成通用名称
		return fmt.Sprintf("server-%d", serverID)
	case "full":
		// 完全脱敏：使用通用名称
		return "server-***"
	default:
		return hostname
	}
}

// 演示脱敏功能
func main() {
	fmt.Println("=== 数据脱敏功能演示 ===\n")

	// 测试不同的IP地址脱敏
	fmt.Println("📍 IP地址脱敏演示:")
	ips := []string{"*************", "********", "**********", "***********"}
	modes := []string{"none", "partial", "full"}

	for _, ip := range ips {
		fmt.Printf("\n原始IP: %s\n", ip)
		for _, mode := range modes {
			masked := maskIPAddress(ip, mode)
			fmt.Printf("  %s 模式: %s\n", mode, masked)
		}
	}

	// 测试主机名脱敏
	fmt.Println("\n🏷️  主机名脱敏演示:")
	hostnames := []string{"web-server-prod", "db-master-01", "cache-redis-cluster"}

	for i, hostname := range hostnames {
		serverID := i + 1
		fmt.Printf("\n原始主机名: %s (服务器ID: %d)\n", hostname, serverID)
		for _, mode := range modes {
			masked := maskHostname(hostname, serverID, mode)
			fmt.Printf("  %s 模式: %s\n", mode, masked)
		}
	}

	// 测试IPv6地址
	fmt.Println("\n🌐 IPv6地址脱敏演示:")
	ipv6s := []string{"2001:db8:85a3::8a2e:370:7334", "fe80::1%lo0"}

	for _, ip := range ipv6s {
		fmt.Printf("\n原始IPv6: %s\n", ip)
		for _, mode := range modes {
			masked := maskIPAddress(ip, mode)
			fmt.Printf("  %s 模式: %s\n", mode, masked)
		}
	}

	fmt.Println("\n=== 演示完成 ===")
	fmt.Println("\n💡 使用说明:")
	fmt.Println("- none: 不进行脱敏，显示完整信息")
	fmt.Println("- partial: 部分脱敏，隐藏敏感部分但保留识别信息")
	fmt.Println("- full: 完全脱敏，最大程度隐藏敏感信息")
}
