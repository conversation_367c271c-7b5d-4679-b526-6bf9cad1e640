import { test, expect } from '@playwright/test';

test.describe('静态构建产物测试', () => {
  test('页面基本加载', async ({ page }) => {
    // 访问静态构建的页面
    await page.goto('/');
    
    // 检查页面是否正确加载
    await expect(page).toHaveTitle(/服务器监控系统/);
    
    // 检查Vue应用是否正确挂载
    await expect(page.locator('#app')).toBeVisible();
  });

  test('CSS样式正确加载', async ({ page }) => {
    await page.goto('/');
    
    // 检查Tailwind CSS是否正确加载
    const body = page.locator('body');
    await expect(body).toBeVisible();
    
    // 检查Element Plus样式是否加载
    await page.waitForTimeout(1000);
    
    // 检查是否有CSS文件加载
    const stylesheets = await page.locator('link[rel="stylesheet"]').count();
    expect(stylesheets).toBeGreaterThan(0);
  });

  test('JavaScript正确执行', async ({ page }) => {
    await page.goto('/');
    
    // 检查Vue应用是否正确初始化
    const vueApp = await page.evaluate(() => {
      return window.Vue !== undefined || document.querySelector('#app').__vue__ !== undefined;
    });
    
    // 等待Vue应用完全加载
    await page.waitForTimeout(2000);
    
    // 检查路由是否工作
    await expect(page).toHaveURL(/\/(login|dashboard)/);
  });

  test('资源文件正确加载', async ({ page }) => {
    // 监听网络请求
    const responses = [];
    page.on('response', response => {
      responses.push({
        url: response.url(),
        status: response.status()
      });
    });
    
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    // 检查是否有失败的资源请求
    const failedRequests = responses.filter(r => r.status >= 400);
    
    if (failedRequests.length > 0) {
      console.log('Failed requests:', failedRequests);
    }
    
    // 大部分资源应该成功加载
    const successfulRequests = responses.filter(r => r.status >= 200 && r.status < 300);
    expect(successfulRequests.length).toBeGreaterThan(0);
  });

  test('移动端响应式设计', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    // 检查页面在移动端是否正常显示
    await expect(page.locator('#app')).toBeVisible();
    
    // 检查是否有横向滚动条
    const hasHorizontalScroll = await page.evaluate(() => {
      return document.body.scrollWidth > window.innerWidth;
    });
    
    expect(hasHorizontalScroll).toBeFalsy();
  });

  test('基本交互功能', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    // 如果在登录页面，测试基本交互
    const isLoginPage = await page.locator('input[placeholder="用户名"]').isVisible();
    
    if (isLoginPage) {
      // 测试表单输入
      await page.fill('input[placeholder="用户名"]', 'test');
      const inputValue = await page.inputValue('input[placeholder="用户名"]');
      expect(inputValue).toBe('test');
      
      // 测试按钮点击
      const loginButton = page.locator('button:has-text("登录")');
      await expect(loginButton).toBeVisible();
      await expect(loginButton).toBeEnabled();
    }
  });

  test('无障碍性检查', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    // 检查页面是否有基本的语义化标签
    await expect(page.locator('h1, h2, h3')).toHaveCount({ min: 1 });
    
    // 检查表单元素是否有适当的标签
    const inputs = page.locator('input');
    const inputCount = await inputs.count();
    
    if (inputCount > 0) {
      // 检查输入框是否有placeholder或label
      for (let i = 0; i < inputCount; i++) {
        const input = inputs.nth(i);
        const hasPlaceholder = await input.getAttribute('placeholder');
        const hasAriaLabel = await input.getAttribute('aria-label');
        
        expect(hasPlaceholder || hasAriaLabel).toBeTruthy();
      }
    }
  });

  test('性能基准测试', async ({ page }) => {
    // 开始性能监控
    await page.goto('/');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 获取性能指标
    const performanceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0];
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime,
        firstContentfulPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-contentful-paint')?.startTime
      };
    });
    
    console.log('Performance metrics:', performanceMetrics);
    
    // 基本性能断言
    expect(performanceMetrics.domContentLoaded).toBeLessThan(5000); // 5秒内DOM加载完成
    expect(performanceMetrics.loadComplete).toBeLessThan(10000); // 10秒内完全加载
  });
});
