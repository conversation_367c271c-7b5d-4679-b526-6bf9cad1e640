import { test, expect } from '@playwright/test';

test.describe('仪表板页面测试', () => {
  test.beforeEach(async ({ page }) => {
    // 模拟已登录状态
    await page.route('/api/servers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 1,
            name: '测试服务器1',
            ipv4: '*************',
            host: {
              Platform: 'Linux',
              PlatformVersion: 'Ubuntu 20.04',
              MemTotal: 8589934592
            },
            status: {
              CPU: 45.5,
              MemUsed: 4294967296,
              Uptime: 86400,
              NetInSpeed: 1024000,
              NetOutSpeed: 512000,
              NetInTransfer: 1073741824,
              NetOutTransfer: 536870912
            },
            last_active: Math.floor(Date.now() / 1000) - 30
          },
          {
            id: 2,
            name: '测试服务器2',
            ipv4: '*************',
            host: {
              Platform: 'Windows',
              PlatformVersion: 'Windows Server 2019',
              MemTotal: 16777216000
            },
            status: {
              CPU: 25.0,
              MemUsed: 8388608000,
              Uptime: 172800,
              NetInSpeed: 2048000,
              NetOutSpeed: 1024000,
              NetInTransfer: 2147483648,
              NetOutTransfer: 1073741824
            },
            last_active: Math.floor(Date.now() / 1000) - 15
          }
        ])
      });
    });

    await page.goto('/dashboard');
  });

  test('页面加载和基本元素', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/服务器监控系统/);
    
    // 检查主要组件
    await expect(page.locator('h1')).toContainText('服务器监控系统');
    await expect(page.locator('.search-container')).toBeVisible();
    await expect(page.locator('.stats-overview')).toBeVisible();
    await expect(page.locator('.servers-grid')).toBeVisible();
  });

  test('统计概览显示', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 检查统计卡片
    const statsCards = page.locator('.stat-card');
    await expect(statsCards).toHaveCount(4);
    
    // 检查统计数据
    await expect(page.locator('.stat-card').first()).toContainText('总服务器');
    await expect(page.locator('.stat-card').nth(1)).toContainText('在线');
    await expect(page.locator('.stat-card').nth(2)).toContainText('离线');
    await expect(page.locator('.stat-card').nth(3)).toContainText('警告');
  });

  test('服务器卡片显示', async ({ page }) => {
    // 等待服务器数据加载
    await page.waitForTimeout(1000);
    
    // 检查服务器卡片
    const serverCards = page.locator('.server-card');
    await expect(serverCards).toHaveCount(12); // 12个占位符卡片
    
    // 检查第一个服务器卡片的内容
    const firstCard = serverCards.first();
    await expect(firstCard).toContainText('测试服务器1');
    await expect(firstCard).toContainText('*************');
    await expect(firstCard).toContainText('Linux Ubuntu 20.04');
    
    // 检查状态指示器
    await expect(firstCard.locator('.status-indicator')).toBeVisible();
    
    // 检查进度条
    await expect(firstCard.locator('.progress-bar')).toBeVisible();
  });

  test('搜索功能', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 测试搜索
    await page.fill('input[placeholder="搜索服务器..."]', '测试服务器1');
    
    // 检查搜索结果
    const visibleCards = page.locator('.server-card:visible');
    await expect(visibleCards).toHaveCount(1);
    await expect(visibleCards.first()).toContainText('测试服务器1');
    
    // 清空搜索
    await page.fill('input[placeholder="搜索服务器..."]', '');
    await expect(page.locator('.server-card')).toHaveCount(12);
  });

  test('状态过滤', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 测试在线状态过滤
    await page.selectOption('select', 'online');
    
    // 检查过滤结果
    const onlineCards = page.locator('.server-card:visible');
    await expect(onlineCards.count()).toBeGreaterThan(0);
    
    // 测试离线状态过滤
    await page.selectOption('select', 'offline');
    
    // 测试全部状态
    await page.selectOption('select', 'all');
    await expect(page.locator('.server-card')).toHaveCount(12);
  });

  test('WebSocket连接状态', async ({ page }) => {
    // 检查WebSocket连接状态指示器
    const wsStatus = page.locator('.ws-status');
    
    // 模拟WebSocket连接
    await page.evaluate(() => {
      // 模拟WebSocket连接成功
      window.dispatchEvent(new CustomEvent('ws-connected'));
    });
    
    // 检查连接状态
    await expect(wsStatus).toContainText('已连接');
  });

  test('实时数据更新', async ({ page }) => {
    // 等待初始数据加载
    await page.waitForTimeout(1000);
    
    // 模拟WebSocket数据更新
    await page.evaluate(() => {
      // 模拟接收到新的服务器数据
      const event = new CustomEvent('server-update', {
        detail: {
          id: 1,
          stats: {
            CPU: 60.0,
            MemUsed: 5368709120,
            NetInSpeed: 2048000,
            NetOutSpeed: 1024000
          }
        }
      });
      window.dispatchEvent(event);
    });
    
    // 检查数据是否更新
    const firstCard = page.locator('.server-card').first();
    await expect(firstCard).toContainText('60%'); // CPU使用率更新
  });

  test('服务器卡片点击跳转', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 点击第一个服务器卡片
    await page.click('.server-card:first-child');
    
    // 检查是否跳转到服务管理页面
    await expect(page).toHaveURL('/services/1');
  });

  test('响应式设计', async ({ page }) => {
    // 测试桌面视图
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('.servers-grid')).toBeVisible();
    
    // 测试平板视图
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('.servers-grid')).toBeVisible();
    
    // 测试移动视图
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('.servers-grid')).toBeVisible();
  });

  test('数据格式化', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    const firstCard = page.locator('.server-card').first();
    
    // 检查内存格式化 (8GB)
    await expect(firstCard).toContainText('8.0 GB');
    
    // 检查网络速度格式化
    await expect(firstCard).toContainText('1.0 MB/s');
    
    // 检查运行时间格式化
    await expect(firstCard).toContainText('1d');
  });

  test('错误处理', async ({ page }) => {
    // 模拟API错误
    await page.route('/api/servers', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' })
      });
    });
    
    await page.reload();
    
    // 检查错误消息
    await expect(page.locator('.error-message')).toBeVisible();
  });

  test('刷新功能', async ({ page }) => {
    // 等待初始加载
    await page.waitForTimeout(1000);
    
    // 点击刷新按钮
    await page.click('button:has-text("刷新")');
    
    // 检查加载状态
    await expect(page.locator('.loading')).toBeVisible();
    
    // 等待加载完成
    await page.waitForTimeout(1000);
    await expect(page.locator('.loading')).not.toBeVisible();
  });

  test('导航到服务管理', async ({ page }) => {
    // 点击导航链接
    await page.click('a:has-text("服务管理")');
    
    // 检查跳转
    await expect(page).toHaveURL('/services');
  });
});
