package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/gorilla/websocket"
	"github.com/shirou/gopsutil/v4/cpu"
	"github.com/shirou/gopsutil/v4/disk"
	"github.com/shirou/gopsutil/v4/host"
	"github.com/shirou/gopsutil/v4/load"
	"github.com/shirou/gopsutil/v4/mem"
	"github.com/shirou/gopsutil/v4/net"
	proc "github.com/shirou/gopsutil/v4/process" // Alias for process package
	"golang.org/x/crypto/pbkdf2"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	_ "modernc.org/sqlite"
)

// [Translated] 用于企业级监控的网络速度跟踪
type NetworkSpeedTracker struct {
	LastNetIn  uint64
	LastNetOut uint64
	LastTime   time.Time
	mutex      sync.RWMutex
}

var networkTracker = &NetworkSpeedTracker{
	LastTime: time.Now(),
}

// [Translated] calculateNetworkSpeed 计算每秒字节数的网络速度
func (nt *NetworkSpeedTracker) calculateNetworkSpeed(currentNetIn, currentNetOut uint64) (uint64, uint64) {
	nt.mutex.Lock()
	defer nt.mutex.Unlock()

	now := time.Now()
	timeDiff := now.Sub(nt.LastTime).Seconds()

	var inSpeed, outSpeed uint64

	// [Translated] 网络速度计算的调试日志
	// log.Printf("Network Speed Debug - Current: In=%d, Out=%d, TimeDiff=%.2fs", currentNetIn, currentNetOut, timeDiff)
	// log.Printf("Network Speed Debug - Previous: In=%d, Out=%d", nt.LastNetIn, nt.LastNetOut)

	// [Translated] 仅在我们有先前数据和合理时间差的情况下计算速度
	// [Translated] 仅在首次运行（当LastTime为初始值时）或时间差过大时跳过计算
	isFirstRun := nt.LastNetIn == 0 && nt.LastNetOut == 0
	if timeDiff > 0 && timeDiff < 300 && !isFirstRun {
		// [Translated] 计算每秒字节数
		if currentNetIn >= nt.LastNetIn {
			inSpeed = uint64(float64(currentNetIn-nt.LastNetIn) / timeDiff)
		}
		if currentNetOut >= nt.LastNetOut {
			outSpeed = uint64(float64(currentNetOut-nt.LastNetOut) / timeDiff)
		}
		// log.Printf("网络速度调试 - 计算结果: 入站速度=%d B/s, 出站速度=%d B/s", inSpeed, outSpeed)
	} else {
		if isFirstRun {
			// log.Printf("网络速度调试 - 首次运行，初始化基线数据")
		} else {
			// log.Printf("网络速度调试 - 跳过计算: 时间差=%.2f, 上次入站=%d, 上次出站=%d", timeDiff, nt.LastNetIn, nt.LastNetOut)
		}
	}

	// [Translated] 更新跟踪数据
	nt.LastNetIn = currentNetIn
	nt.LastNetOut = currentNetOut
	nt.LastTime = now

	return inSpeed, outSpeed
}

type ServerConfig struct {
	Listen        string `json:"listen"`
	Port          string `json:"port"`
	LoginUsername string `json:"login_username"`
	LoginPassword string `json:"login_password"`
	Servers       []struct {
		ID      int    `json:"mid"`
		Name    string `json:"name"`
		Host    string `json:"host"`
		Enabled bool   `json:"enabled"`
	} `json:"servers"`
	Database struct {
		Type string `json:"type"`
		Path string `json:"path"`
	} `json:"database"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	Password string `json:"password"`
}

type ClientConfig struct {
	MID       int    `json:"mid"`
	Server    string `json:"server"`
	Port      string `json:"port"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	Database struct {
		Type      string `json:"type"`
		Path      string `json:"path"`
		Retention string `json:"retention"`
	} `json:"database"`
	Password   string `json:"password"`
	ServerInfo struct {
		Name         string `json:"name"`
		Tag          string `json:"tag"`
		IPv4         string `json:"ipv4"`
		IPv6         string `json:"ipv6"`
		AutoDetectIP bool   `json:"auto_detect_ip"`
	} `json:"server_info"`
}

// [Translated] 主机信息结构
type HostInfo struct {
	Platform        string `json:"Platform"`
	PlatformVersion string `json:"PlatformVersion"`
	CPU             string `json:"CPU" gorm:"type:text"` // [Translated] 存储为JSON字符串
	MemTotal        uint64 `json:"MemTotal"`
	DiskTotal       uint64 `json:"DiskTotal"`
	SwapTotal       uint64 `json:"SwapTotal"`
	Arch            string `json:"Arch"`
	Virtualization  string `json:"Virtualization"`
	BootTime        int64  `json:"BootTime"`
	CountryCode     string `json:"CountryCode"`
	Version         string `json:"Version"`
}

// [Translated] 状态信息结构
type StatusInfo struct {
	CPU            float64 `json:"CPU"`
	MemUsed        uint64  `json:"MemUsed"`
	MemTotal       uint64  `json:"MemTotal"`
	SwapUsed       uint64  `json:"SwapUsed"`
	DiskUsed       uint64  `json:"DiskUsed"`
	DiskTotal      uint64  `json:"DiskTotal"`
	NetInTransfer  uint64  `json:"NetInTransfer"`
	NetOutTransfer uint64  `json:"NetOutTransfer"`
	NetInSpeed     uint64  `json:"NetInSpeed"`
	NetOutSpeed    uint64  `json:"NetOutSpeed"`
	Uptime         uint64  `json:"Uptime"`
	Load1          float64 `json:"Load1"`
	Load5          float64 `json:"Load5"`
	Load15         float64 `json:"Load15"`
	TcpConnCount   int     `json:"TcpConnCount"`
	UdpConnCount   int     `json:"UdpConnCount"`
	ProcessCount   int     `json:"ProcessCount"`
}

// [Translated] 实时监控表（轻量级）
type ServerStatus struct {
	ID                int     `gorm:"primaryKey"`
	Name              string  `json:"name"`
	Tag               string  `json:"tag"`
	LastActive        int64   `json:"last_active"`
	IPv4              string  `json:"ipv4"`
	IPv6              string  `json:"ipv6"`
	ValidIP           string  `json:"valid_ip"`
	StatusCPU         float64 `json:"status_cpu"`
	StatusMemUsed     uint64  `json:"status_mem_used"`
	StatusMemTotal    uint64  `json:"status_mem_total"`
	StatusDiskUsed    uint64  `json:"status_disk_used"`
	StatusDiskTotal   uint64  `json:"status_disk_total"`
	StatusNetInSpeed  uint64  `json:"status_net_in_speed"`
	StatusNetOutSpeed uint64  `json:"status_net_out_speed"`
	StatusUptime      uint64  `json:"status_uptime"`
	StatusLoad1       float64 `json:"status_load1"`
	StatusLoad5       float64 `json:"status_load5"`
	StatusLoad15      float64 `json:"status_load15"`
	gorm.Model
}

// [Translated] 服务器信息表（静态）
type ServerInfo struct {
	ID                  int    `gorm:"primaryKey"`
	HostPlatform        string `json:"host_platform"`
	HostPlatformVersion string `json:"host_platform_version"`
	HostCPU             string `json:"host_cpu"`
	HostArch            string `json:"host_arch"`
	HostVirtualization  string `json:"host_virtualization"`
	HostBootTime        int64  `json:"host_boot_time"`
	HostCountryCode     string `json:"host_country_code"`
	HostVersion         string `json:"host_version"`
	gorm.Model
}

// [Translated] 历史监控数据表
type ServerStatusHistory struct {
	ID                uint      `gorm:"primaryKey"` // [Translated] 添加主键
	ServerID          int       `gorm:"index"`      // 外键，关联到 ServerStatus
	Timestamp         time.Time `gorm:"index"`
	StatusCPU         float64
	StatusMemUsed     uint64
	StatusMemTotal    uint64
	StatusDiskUsed    uint64
	StatusDiskTotal   uint64
	StatusNetInSpeed  uint64
	StatusNetOutSpeed uint64
	StatusUptime      uint64
	StatusLoad1       float64
	StatusLoad5       float64
	StatusLoad15      float64
}

// [Translated] 完整的服务器详细信息结构 (用于数据处理，不直接映射到单个表)
type ServerDetails struct {
	ID         int        `json:"id" gorm:"primaryKey"`
	Name       string     `json:"name"`
	Tag        string     `json:"tag"`
	LastActive int64      `json:"last_active"`
	IPv4       string     `json:"ipv4"`
	IPv6       string     `json:"ipv6"`
	ValidIP    string     `json:"valid_ip"`
	Host       HostInfo   `json:"host" gorm:"-"`   // [Translated] 忽略此字段的GORM映射
	Status     StatusInfo `json:"status" gorm:"-"` // [Translated] 忽略此字段的GORM映射
	// Add associations for services (GORM will handle the join tables)
	SupervisorServices []SupervisorService `json:"supervisor_services" gorm:"foreignKey:ServerID"`
	SystemdServices    []SystemdService    `json:"systemd_services" gorm:"foreignKey:ServerID"`
	DockerServices     []DockerService     `json:"docker_services" gorm:"foreignKey:ServerID"`
	gorm.Model
}

// [Translated] WebSocket消息结构（用于客户端连接）
type Message struct {
	Type          string        `json:"type"`
	Password      string        `json:"password"`
	Data          ServerDetails `json:"data,omitempty"`
	EncryptedData string        `json:"encrypted_data,omitempty"`
	Encrypted     bool          `json:"encrypted"`
	Timestamp     time.Time     `json:"timestamp"`
	// New fields for service data
	SupervisorServices []Service `json:"supervisor_services,omitempty"`
	SystemdServices    []Service `json:"systemd_services,omitempty"`
	DockerServices     []Service `json:"docker_services,omitempty"`
}

// [Translated] 前端WebSocket消息结构
type FrontendWSMessage struct {
	Type      string      `json:"type"`
	Token     string      `json:"token,omitempty"`
	ServerID  int         `json:"server_id,omitempty"`
	Data      interface{} `json:"data,omitempty"`
	Error     string      `json:"error,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// [Translated] 前端客户端的WebSocket连接信息
type FrontendConnection struct {
	Conn         *websocket.Conn
	UserID       string
	SubscribedTo map[int]bool // [Translated] 此连接订阅的服务器ID
	LastPing     time.Time
}

// [Translated] 前端WebSocket连接的连接管理器
type ConnectionManager struct {
	connections map[string]*FrontendConnection
	mutex       sync.RWMutex
}

// [Translated] ServiceRequest表示服务操作请求
type ServiceRequest struct {
	ServerID    int    `json:"serverId"`
	ServiceName string `json:"serviceName"`
	ServiceType string `json:"serviceType"`
}

// [Translated] Service表示服务信息
type Service struct {
	Name        string `json:"name"`
	Status      string `json:"status"`
	Description string `json:"description,omitempty"`
	PID         string `json:"pid,omitempty"`
	Uptime      int64  `json:"uptime,omitempty"` // Changed to int64
	Memory      string `json:"memory,omitempty"`
	CPU         string `json:"cpu,omitempty"`
	Type        string `json:"type"` // [Translated] supervisor, systemd, docker
}

// New GORM models for services
type SupervisorService struct {
	gorm.Model
	ServerID    int    `json:"server_id"` // Foreign key to ServerDetails
	Name        string `json:"name"`
	Status      string `json:"status"`
	Description string `json:"description,omitempty"`
	PID         string `json:"pid,omitempty"`
	Uptime      int64  `json:"uptime,omitempty"` // Changed to int64
	Memory      string `json:"memory,omitempty"`
	CPU         string `json:"cpu,omitempty"`
}

type SystemdService struct {
	gorm.Model
	ServerID    int    `json:"server_id"` // Foreign key to ServerDetails
	Name        string `json:"name"`
	Status      string `json:"status"`
	Description string `json:"description,omitempty"`
	PID         string `json:"pid,omitempty"`
	Uptime      int64  `json:"uptime,omitempty"` // Changed to int64
	Memory      string `json:"memory,omitempty"`
	CPU         string `json:"cpu,omitempty"`
}

type DockerService struct {
	gorm.Model
	ServerID    int    `json:"server_id"` // Foreign key to ServerDetails
	Name        string `json:"name"`
	Status      string `json:"status"`
	Description string `json:"description,omitempty"`
	PID         string `json:"pid,omitempty"`
	Uptime      int64  `json:"uptime,omitempty"` // Changed to int64
	Memory      string `json:"memory,omitempty"`
	CPU         string `json:"cpu,omitempty"`
}

// [Translated] LoginRequest表示登录请求
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// [Translated] LoginResponse表示登录响应
type LoginResponse struct {
	Success bool   `json:"success"`
	Token   string `json:"token,omitempty"`
	Message string `json:"message,omitempty"`
}

// [Translated] Claims表示JWT声明
type Claims struct {
	Username string `json:"username"`
	jwt.RegisteredClaims
}

var (
	jwtSecret   = getJwtSecret() // [Translated] 从环境变量获取JWT密钥
	tokenExpiry = 20 * time.Minute
)

func getJwtSecret() string {
	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		log.Fatal("JWT_SECRET environment variable not set. Please set it for security.")
	}
	return secret
}

var (
	isClient     = flag.Bool("c", false, "Run as client")
	isServer     = flag.Bool("s", false, "Run as server")
	configFile   = flag.String("f", "", "Configuration file path")
	db           *gorm.DB
	dbMutex      sync.RWMutex // [Translated] 为数据库操作添加读写锁
	serverConfig *ServerConfig
	upgrader     = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // [Translated] 为简化起见，允许所有来源
		},
	}
	frontendConnManager = &ConnectionManager{
		connections: make(map[string]*FrontendConnection),
	}
)

// [Translated] 内存缓存用于存储最新的服务器状态
var serverStatusCache = struct {
	sync.RWMutex
	data map[int]*StatusInfo
}{
	data: make(map[int]*StatusInfo),
}

func main() {
	flag.Parse()

	if !*isClient && !*isServer {
		fmt.Println("Please specify either -c (client) or -s (server)")
		flag.Usage()
		os.Exit(1)
	}

	if *configFile == "" {
		if *isServer {
			*configFile = "server.json"
		} else {
			*configFile = "client.json"
		}
	}

	if *isServer {
		runServer()
	} else {
		runClient()
	}
}

func runServer() {
	fmt.Println("Starting server mode...")

	config, err := loadServerConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load server config: %v", err)
	}

	// [Translated] 全局存储配置以供处理程序使用
	serverConfig = config

	// [Translated] 初始化数据库
	dbPath := config.Database.Path
	if dbPath == "" {
		dbPath = "server.db"
	}
	initDatabase(dbPath)

	// [Translated] 设置客户端连接的WebSocket处理程序
	http.HandleFunc(config.WebSocket.Path, func(w http.ResponseWriter, r *http.Request) {
		handleWebSocket(w, r, config.Password)
	})

	// [Translated] 设置前端连接的WebSocket处理程序
	http.HandleFunc("/ws/frontend", handleFrontendWebSocket)

	// [Translated] 设置认证端点
	http.HandleFunc("/api/login", handleLogin)
	http.HandleFunc("/api/refresh", handleRefreshToken)

	// [Translated] 设置受保护的API端点
	http.HandleFunc("/api/servers", authMiddleware(getServersAPI))
	http.HandleFunc("/api/system/stats", authMiddleware(getSystemStatsAPI))
	http.HandleFunc("/api/services/list", authMiddleware(handleListServicesAPI))
	http.HandleFunc("/api/services/supervisor/start", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/supervisor/stop", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/supervisor/restart", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/systemd/start", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/systemd/stop", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/systemd/restart", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/docker/start", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/docker/stop", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/docker/restart", authMiddleware(handleControlServiceAPI))
	http.HandleFunc("/api/services/logs", authMiddleware(handleGetServiceLogsAPI))

	// [Translated] 设置静态文件服务
	http.HandleFunc("/", serveIndex)

	// [Translated] 启动实时数据广播
	go startRealTimeDataBroadcast()

	// [Translated] 启动HTTP服务器
	addr := fmt.Sprintf("%s:%s", config.Listen, config.Port)
	fmt.Printf("Server listening on %s%s\n", addr, config.WebSocket.Path)
	fmt.Printf("Frontend WebSocket available at ws://%s/ws/frontend\n", addr)
	fmt.Printf("Web interface available at http://%s/\n", addr)
	fmt.Printf("Login endpoint available at http://%s/api/login\n", addr)
	fmt.Printf("API endpoint available at http://%s/api/servers\n", addr)
	log.Fatal(http.ListenAndServe(addr, nil))
}

func runClient() {
	// fmt.Println("Starting client mode...") // Commented out to reduce client output

	config, err := loadClientConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load client config: %v", err)
	}

	// [Translated] 初始化本地数据库
	dbPath := config.Database.Path
	if dbPath == "" {
		dbPath = "client.db"
	}
	initDatabase(dbPath)

	// [Translated] 连接到服务器并开始监控
	connectAndMonitor(config)
}

func initDatabase(dbPath string) {
	var err error
	db, err = gorm.Open(sqlite.Dialector{
		DriverName: "sqlite",
		DSN:        dbPath,
	}, &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// [Translated] 启用WAL模式并优化性能
	db.Exec("PRAGMA journal_mode=WAL;")
	db.Exec("PRAGMA synchronous=NORMAL;")
	db.Exec("PRAGMA cache_size=10000;")
	db.Exec("PRAGMA temp_store=MEMORY;")
	db.Exec("PRAGMA busy_timeout=5000;")

	// [Translated] 自动迁移模式
	err = db.AutoMigrate(&ServerStatus{}, &ServerInfo{}, &SupervisorService{}, &SystemdService{}, &DockerService{}, &ServerStatusHistory{})
	if err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	fmt.Printf("Database initialized with WAL mode: %s\n", dbPath)
}

// [Translated] handleLogin处理用户登录请求
func handleLogin(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	// [Translated] 更新CORS配置以支持credentials
	w.Header().Set("Access-Control-Allow-Origin", r.Header.Get("Origin"))
	w.Header().Set("Access-Control-Allow-Credentials", "true")
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		w.WriteHeader(http.StatusMethodNotAllowed)
		json.NewEncoder(w).Encode(LoginResponse{
			Success: false,
			Message: "Method not allowed",
		})
		return
	}

	var loginReq LoginRequest
	err := json.NewDecoder(r.Body).Decode(&loginReq)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(LoginResponse{
			Success: false,
			Message: "Invalid JSON format",
		})
		return
	}

	// [Translated] 验证凭据
	if loginReq.Username != serverConfig.LoginUsername || loginReq.Password != serverConfig.LoginPassword {
		w.WriteHeader(http.StatusUnauthorized)
		json.NewEncoder(w).Encode(LoginResponse{
			Success: false,
			Message: "Invalid username or password",
		})
		return
	}

	// [Translated] 生成JWT令牌
	token, err := generateJWT(loginReq.Username)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(LoginResponse{
			Success: false,
			Message: "Failed to generate token",
		})
		return
	}

	// [Translated] 设置httpOnly cookie而不是返回token
	http.SetCookie(w, &http.Cookie{
		Name:     "authToken",
		Value:    token,
		Path:     "/",
		HttpOnly: true,
		Secure:   false, // [Translated] 在HTTP环境下设为false，HTTPS环境下应设为true
		SameSite: http.SameSiteStrictMode,
		MaxAge:   int(tokenExpiry.Seconds()),
	})

	// [Translated] 返回成功响应，但不包含token
	json.NewEncoder(w).Encode(LoginResponse{
		Success: true,
		Message: "Login successful",
	})
}

// [Translated] handleRefreshToken处理token刷新请求
func handleRefreshToken(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	// [Translated] 更新CORS配置以支持credentials
	w.Header().Set("Access-Control-Allow-Origin", r.Header.Get("Origin"))
	w.Header().Set("Access-Control-Allow-Credentials", "true")
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		w.WriteHeader(http.StatusMethodNotAllowed)
		json.NewEncoder(w).Encode(LoginResponse{
			Success: false,
			Message: "Method not allowed",
		})
		return
	}

	// [Translated] 从cookie或header获取当前token
	var currentToken string
	if cookie, err := r.Cookie("authToken"); err == nil && cookie.Value != "" {
		currentToken = cookie.Value
	} else {
		// [Translated] 向后兼容：从Authorization header读取token
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			w.WriteHeader(http.StatusUnauthorized)
			json.NewEncoder(w).Encode(LoginResponse{
				Success: false,
				Message: "No valid token found",
			})
			return
		}
		currentToken = strings.TrimPrefix(authHeader, "Bearer ")
		if currentToken == authHeader {
			w.WriteHeader(http.StatusUnauthorized)
			json.NewEncoder(w).Encode(LoginResponse{
				Success: false,
				Message: "Invalid authorization format",
			})
			return
		}
	}

	// [Translated] 验证当前token
	claims, err := validateJWT(currentToken)
	if err != nil {
		w.WriteHeader(http.StatusUnauthorized)
		json.NewEncoder(w).Encode(LoginResponse{
			Success: false,
			Message: "Invalid or expired token",
		})
		return
	}

	// [Translated] 检查token是否即将过期（提前5分钟刷新）
	expiryTime := claims.ExpiresAt.Time
	refreshThreshold := time.Now().Add(5 * time.Minute)

	if expiryTime.After(refreshThreshold) {
		// [Translated] token还有超过5分钟的有效期，无需刷新
		json.NewEncoder(w).Encode(LoginResponse{
			Success: true,
			Message: "Token is still valid",
		})
		return
	}

	// [Translated] 生成新的JWT token
	newToken, err := generateJWT(claims.Username)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(LoginResponse{
			Success: false,
			Message: "Failed to generate new token",
		})
		return
	}

	// [Translated] 设置新的httpOnly cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "authToken",
		Value:    newToken,
		Path:     "/",
		HttpOnly: true,
		Secure:   false, // [Translated] 在HTTP环境下设为false，HTTPS环境下应设为true
		SameSite: http.SameSiteStrictMode,
		MaxAge:   int(tokenExpiry.Seconds()),
	})

	// [Translated] 返回成功响应
	json.NewEncoder(w).Encode(LoginResponse{
		Success: true,
		Message: "Token refreshed successfully",
	})
}

// [Translated] generateJWT为给定用户名生成JWT令牌
func generateJWT(username string) (string, error) {
	claims := Claims{
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(tokenExpiry)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "vps-monitor",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(jwtSecret))
}

// [Translated] validateJWT验证JWT令牌并返回声明
func validateJWT(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(jwtSecret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// [Translated] authMiddleware是一个验证JWT令牌的中间件
func authMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// [Translated] 更新CORS配置以支持credentials
		w.Header().Set("Access-Control-Allow-Origin", r.Header.Get("Origin"))
		w.Header().Set("Access-Control-Allow-Credentials", "true")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		var tokenString string

		// [Translated] 优先从cookie读取token
		if cookie, err := r.Cookie("authToken"); err == nil && cookie.Value != "" {
			tokenString = cookie.Value
		} else {
			// [Translated] 向后兼容：从Authorization header读取token
			authHeader := r.Header.Get("Authorization")
			if authHeader == "" {
				w.WriteHeader(http.StatusUnauthorized)
				json.NewEncoder(w).Encode(map[string]string{
					"error": "Authentication required",
				})
				return
			}

			// [Translated] 从"Bearer <token>"中提取令牌
			tokenString = strings.TrimPrefix(authHeader, "Bearer ")
			if tokenString == authHeader {
				w.WriteHeader(http.StatusUnauthorized)
				json.NewEncoder(w).Encode(map[string]string{
					"error": "Invalid authorization format",
				})
				return
			}
		}

		// [Translated] 验证令牌
		_, err := validateJWT(tokenString)
		if err != nil {
			w.WriteHeader(http.StatusUnauthorized)
			json.NewEncoder(w).Encode(map[string]string{
				"error": "Invalid or expired token",
			})
			return
		}

		// [Translated] 令牌有效，继续下一个处理程序
		next(w, r)
	}
}

func handleWebSocket(w http.ResponseWriter, r *http.Request, password string) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}
	defer conn.Close()

	fmt.Printf("Client connected: %s\n", r.RemoteAddr)

	for {
		var msg Message
		err := conn.ReadJSON(&msg)
		if err != nil {
			log.Printf("Read error: %v", err)
			break
		}

		// [Translated] 验证密码
		if msg.Password != password {
			log.Printf("Invalid password from %s", r.RemoteAddr)
			continue
		}

		var serverDetails ServerDetails

		if msg.Encrypted && msg.EncryptedData != "" {
			// [Translated] 解密数据
			decryptedData, err := decrypt(msg.EncryptedData, password)
			if err != nil {
				log.Printf("Failed to decrypt data from %s: %v", r.RemoteAddr, err)
				continue
			}

			// [Translated] 解密数据
			err = json.Unmarshal(decryptedData, &serverDetails)
			if err != nil {
				log.Printf("Failed to unmarshal decrypted data from %s: %v", r.RemoteAddr, err)
				continue
			}
		} else {
			// [Translated] 使用未加密数据（向后兼容）
			serverDetails = msg.Data
		}

		// [Translated] 将数据存储到数据库
		serverDetails.LastActive = time.Now().Unix()

		// [Translated] 创建或更新 ServerStatus
		serverStatus := ServerStatus{
			ID:                serverDetails.ID,
			Name:              serverDetails.Name,
			Tag:               serverDetails.Tag,
			LastActive:        serverDetails.LastActive,
			IPv4:              serverDetails.IPv4,
			IPv6:              serverDetails.IPv6,
			ValidIP:           serverDetails.ValidIP,
			StatusCPU:         serverDetails.Status.CPU,
			StatusMemUsed:     serverDetails.Status.MemUsed,
			StatusMemTotal:    serverDetails.Status.MemTotal,
			StatusDiskUsed:    serverDetails.Status.DiskUsed,
			StatusDiskTotal:   serverDetails.Status.DiskTotal,
			StatusNetInSpeed:  serverDetails.Status.NetInSpeed,
			StatusNetOutSpeed: serverDetails.Status.NetOutSpeed,
			StatusUptime:      serverDetails.Status.Uptime,
			StatusLoad1:       serverDetails.Status.Load1,
			StatusLoad5:       serverDetails.Status.Load5,
			StatusLoad15:      serverDetails.Status.Load15,
		}
		if err := saveWithRetry(&serverStatus, 3); err != nil {
			log.Printf("Database error (ServerStatus) after retries: %v", err)
		}

		// [Translated] 更新内存缓存
		serverStatusCache.Lock()
		serverStatusCache.data[serverStatus.ID] = &StatusInfo{
			CPU:         serverStatus.StatusCPU,
			MemUsed:     serverStatus.StatusMemUsed,
			MemTotal:    serverStatus.StatusMemTotal,
			DiskUsed:    serverStatus.StatusDiskUsed,
			DiskTotal:   serverStatus.StatusDiskTotal,
			NetInSpeed:  serverStatus.StatusNetInSpeed,
			NetOutSpeed: serverStatus.StatusNetOutSpeed,
			Uptime:      serverStatus.StatusUptime,
			Load1:       serverStatus.StatusLoad1,
			Load5:       serverStatus.StatusLoad5,
			Load15:      serverStatus.StatusLoad15,
		}
		serverStatusCache.Unlock()

		// [Translated] 创建或更新 ServerInfo
		serverInfo := ServerInfo{
			ID:                  serverDetails.ID,
			HostPlatform:        serverDetails.Host.Platform,
			HostPlatformVersion: serverDetails.Host.PlatformVersion,
			HostCPU:             serverDetails.Host.CPU,
			HostArch:            serverDetails.Host.Arch,
			HostVirtualization:  serverDetails.Host.Virtualization,
			HostBootTime:        serverDetails.Host.BootTime,
			HostCountryCode:     serverDetails.Host.CountryCode,
			HostVersion:         serverDetails.Host.Version,
		}
		if err := saveWithRetry(&serverInfo, 3); err != nil {
			log.Printf("Database error (ServerInfo) after retries: %v", err)
		}

		// [Translated] 保存历史监控数据
		serverStatusHistory := ServerStatusHistory{
			ServerID:          serverStatus.ID,
			Timestamp:         time.Now(),
			StatusCPU:         serverStatus.StatusCPU,
			StatusMemUsed:     serverStatus.StatusMemUsed,
			StatusMemTotal:    serverStatus.StatusMemTotal,
			StatusDiskUsed:    serverStatus.StatusDiskUsed,
			StatusDiskTotal:   serverStatus.StatusDiskTotal,
			StatusNetInSpeed:  serverStatus.StatusNetInSpeed,
			StatusNetOutSpeed: serverStatus.StatusNetOutSpeed,
			StatusUptime:      serverStatus.StatusUptime,
			StatusLoad1:       serverStatus.StatusLoad1,
			StatusLoad5:       serverStatus.StatusLoad5,
			StatusLoad15:      serverStatus.StatusLoad15,
		}
		if err := createWithRetry(&serverStatusHistory, 3); err != nil {
			log.Printf("Database error (ServerStatusHistory) after retries: %v", err)
		}

		// [Translated] 异步处理服务数据
		go processServiceUpdates(serverDetails.ID, msg.SupervisorServices, msg.SystemdServices, msg.DockerServices)
	}

	fmt.Printf("Client disconnected: %s\n", r.RemoteAddr)
}

// [Translated] 获取所有服务器数据的API处理程序
func getServersAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")

	var responseServers []ServerDetails

	dbMutex.RLock()
	defer dbMutex.RUnlock()

	for _, s := range serverConfig.Servers {
		var serverStatus ServerStatus
		// [Translated] 尝试从数据库中查找服务器的最新状态
		result := db.Where("id = ?", s.ID).Order("last_active DESC").First(&serverStatus)

		if result.Error != nil {
			if result.Error == gorm.ErrRecordNotFound {
				// [Translated] 如果未找到记录，则创建默认的ServerDetails，表示服务器离线
				responseServers = append(responseServers, ServerDetails{
					ID:         s.ID,
					Name:       s.Name,
					LastActive: 0,
					Status:     StatusInfo{MemTotal: 1},
				})
				log.Printf("Server ID %d (%s) not found in database, returning default offline status.", s.ID, s.Name)
			} else {
				log.Printf("Database error fetching server ID %d: %v", s.ID, result.Error)
				responseServers = append(responseServers, ServerDetails{
					ID:         s.ID,
					Name:       s.Name,
					LastActive: 0,
					Status:     StatusInfo{MemTotal: 1},
				})
			}
			continue
		}

		var serverInfo ServerInfo
		db.Where("id = ?", s.ID).First(&serverInfo)

		// [Translated] 组合数据
		serverDetail := ServerDetails{
			ID:         serverStatus.ID,
			Name:       serverStatus.Name,
			Tag:        serverStatus.Tag,
			LastActive: serverStatus.LastActive,
			IPv4:       serverStatus.IPv4,
			IPv6:       serverStatus.IPv6,
			ValidIP:    serverStatus.ValidIP,
			Host: HostInfo{
				Platform:        serverInfo.HostPlatform,
				PlatformVersion: serverInfo.HostPlatformVersion,
				CPU:             serverInfo.HostCPU,
				Arch:            serverInfo.HostArch,
				Virtualization:  serverInfo.HostVirtualization,
				BootTime:        serverInfo.HostBootTime,
				CountryCode:     serverInfo.HostCountryCode,
				Version:         serverInfo.HostVersion,
			},
			Status: StatusInfo{
				CPU:         serverStatus.StatusCPU,
				MemUsed:     serverStatus.StatusMemUsed,
				MemTotal:    serverStatus.StatusMemTotal,
				DiskUsed:    serverStatus.StatusDiskUsed,
				DiskTotal:   serverStatus.StatusDiskTotal,
				NetInSpeed:  serverStatus.StatusNetInSpeed,
				NetOutSpeed: serverStatus.StatusNetOutSpeed,
				Uptime:      serverStatus.StatusUptime,
				Load1:       serverStatus.StatusLoad1,
				Load5:       serverStatus.StatusLoad5,
				Load15:      serverStatus.StatusLoad15,
			},
		}

		// [Translated] 将CPU JSON字符串转换回数组以供显示
		var cpuArray []string
		if err := json.Unmarshal([]byte(serverDetail.Host.CPU), &cpuArray); err == nil {
			serverDetail.Host.CPU = strings.Join(cpuArray, ", ")
		} else {
			log.Printf("Failed to unmarshal CPU info for server ID %d: %v", s.ID, err)
			serverDetail.Host.CPU = "N/A" // [Translated] 如果无法解析，则设置为N/A
		}
		responseServers = append(responseServers, serverDetail)
	}

	json.NewEncoder(w).Encode(responseServers)
}

// [Translated] 获取实时系统统计信息的API处理程序
func getSystemStatsAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")

	// [Translated] 检查查询参数中是否提供了serverID
	serverIDStr := r.URL.Query().Get("serverId")
	if serverIDStr != "" {
		// [Translated] 从数据库获取客户端统计信息
		serverID, err := strconv.Atoi(serverIDStr)
		if err != nil {
			log.Printf("Invalid serverID parameter: %v", err)
			http.Error(w, "Invalid serverID parameter", http.StatusBadRequest)
			return
		}

		stats, found := getLatestClientStats(serverID)
		if !found {
			log.Printf("Failed to get client stats for server %d: %v", serverID, err)
			http.Error(w, "Failed to get client statistics", http.StatusInternalServerError)
			return
		}

		json.NewEncoder(w).Encode(stats)
		return
	}

	// [Translated] 回退：获取服务器自己的系统统计信息
	stats, err := getCurrentSystemStats()
	if err != nil {
		log.Printf("Failed to get system stats: %v", err)
		http.Error(w, "Failed to get system statistics", http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(stats)
}

// [Translated] getCurrentSystemStats获取当前系统统计信息
func getCurrentSystemStats() (*StatusInfo, error) {
	// [Translated] 获取CPU使用率
	cpuPercent, err := cpu.Percent(time.Second, false)
	var cpuUsage float64
	if err == nil && len(cpuPercent) > 0 {
		cpuUsage = cpuPercent[0]
	}

	// [Translated] 获取内存信息
	memInfo, err := mem.VirtualMemory()
	if err != nil {
		log.Printf("Error getting memory info in getCurrentSystemStats: %v", err)
		return nil, fmt.Errorf("failed to get memory info: %v", err)
	}
	log.Printf("Debug: getCurrentSystemStats - MemTotal: %d", memInfo.Total)

	// [Translated] 获取磁盘信息
	diskInfo, err := disk.Usage("/")
	if err != nil {
		diskInfo, err = disk.Usage("C:")
		if err != nil {
			return nil, fmt.Errorf("failed to get disk info: %v", err)
		}
	}

	// [Translated] 获取平均负载
	loadInfo, err := load.Avg()
	var load1, load5, load15 float64
	if err == nil {
		load1 = loadInfo.Load1
		load5 = loadInfo.Load5
		load15 = loadInfo.Load15
	}

	// [Translated] 获取网络I/O
	netInfo, err := net.IOCounters(false)
	var netIn, netOut uint64
	if err == nil && len(netInfo) > 0 {
		netIn = netInfo[0].BytesRecv
		netOut = netInfo[0].BytesSent
	}

	// [Translated] 使用企业级跟踪计算网络速度
	netInSpeed, netOutSpeed := networkTracker.calculateNetworkSpeed(netIn, netOut)

	// [Translated] 获取交换信息
	swapInfo, _ := mem.SwapMemory()
	var swapUsed uint64
	if swapInfo != nil {
		swapUsed = swapInfo.Used
	}

	// [Translated] 获取主机正常运行时间信息
	hostInfo, err := host.Info()
	var uptime uint64
	if err == nil {
		uptime = uint64(time.Now().Unix() - int64(hostInfo.BootTime))
	}

	// [Translated] 创建状态信息
	stats := &StatusInfo{
		CPU:            cpuUsage,
		MemUsed:        memInfo.Used,
		MemTotal:       memInfo.Total,
		SwapUsed:       swapUsed,
		DiskUsed:       diskInfo.Used,
		DiskTotal:      diskInfo.Total,
		NetInTransfer:  netIn,
		NetOutTransfer: netOut,
		NetInSpeed:     netInSpeed,  // [Translated] 实时下载速度
		NetOutSpeed:    netOutSpeed, // [Translated] 实时上传速度
		Uptime:         uptime,
		Load1:          load1,
		Load5:          load5,
		Load15:         load15,
		TcpConnCount:   0, // [Translated] 需要额外实现
		UdpConnCount:   0, // [Translated] 需要额外实现
		ProcessCount:   int(hostInfo.Procs),
	}

	return stats, nil
}

// [Translated] 提供静态文件
func serveIndex(w http.ResponseWriter, r *http.Request) {
	// [Translated] 设置CSP安全头，防止XSS攻击
	cspHeader := "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.tailwindcss.com unpkg.com; style-src 'self' 'unsafe-inline' unpkg.com; connect-src 'self' ws: wss:;"
	w.Header().Set("Content-Security-Policy", cspHeader)

	// [Translated] 处理登录页面
	if r.URL.Path == "/login" || r.URL.Path == "/login.html" {
		http.ServeFile(w, r, getFilePath("web/login.html"))
		return
	}

	// [Translated] 处理根路径和登录页面
	if r.URL.Path == "/" || r.URL.Path == "/login" || r.URL.Path == "/login.html" {
		http.ServeFile(w, r, getFilePath("web/login.html"))
		return
	}

	// [Translated] 处理仪表板（登录后的主页）
	if r.URL.Path == "/dashboard" || r.URL.Path == "/index.html" {
		http.ServeFile(w, r, getFilePath("web/index.html"))
		return
	}

	// [Translated] 对于其他路径，尝试直接提供文件
	filePath := r.URL.Path[1:] // [Translated] 移除前导斜杠
	http.ServeFile(w, r, getFilePath("web/"+filePath))
}

// [Translated] execCommand执行shell命令并返回其输出或错误。
func handleListServicesAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")

	serviceType := r.URL.Query().Get("serviceType")
	serverIDStr := r.URL.Query().Get("serverId")

	log.Printf("List services request: serviceType=%s, serverId=%s", serviceType, serverIDStr)

	if serviceType == "" {
		log.Printf("Missing serviceType parameter")
		errorMsg := `{"error": "serviceType parameter is required"}`
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(errorMsg))
		return
	}

	serverID, err := strconv.Atoi(serverIDStr)
	if err != nil {
		log.Printf("Invalid serverID parameter: %v", err)
		http.Error(w, "Invalid serverID parameter", http.StatusBadRequest)
		return
	}

	var services []Service
	switch serviceType {
	case "supervisor":
		var supervisorServices []SupervisorService
		dbMutex.RLock()
		err := db.Where("server_id = ?", serverID).Find(&supervisorServices).Error
		dbMutex.RUnlock()
		if err != nil {
			log.Printf("Failed to get Supervisor services for ServerID %d: %v", serverID, err)
			http.Error(w, "Failed to get Supervisor services", http.StatusInternalServerError)
			return
		}
		for _, s := range supervisorServices {
			services = append(services, Service{
				Name:        s.Name,
				Status:      s.Status,
				Description: s.Description,
				PID:         s.PID,
				Uptime:      s.Uptime,
				Memory:      s.Memory,
				CPU:         s.CPU,
				Type:        "supervisor",
			})
		}
	case "systemd":
		var systemdServices []SystemdService
		dbMutex.RLock()
		err := db.Where("server_id = ?", serverID).Find(&systemdServices).Error
		dbMutex.RUnlock()
		if err != nil {
			log.Printf("Failed to get Systemd services for ServerID %d: %v", serverID, err)
			http.Error(w, "Failed to get Systemd services", http.StatusInternalServerError)
			return
		}
		for _, s := range systemdServices {
			services = append(services, Service{
				Name:        s.Name,
				Status:      s.Status,
				Description: s.Description,
				PID:         s.PID,
				Uptime:      s.Uptime,
				Memory:      s.Memory,
				CPU:         s.CPU,
				Type:        "systemd",
			})
		}
	case "docker":
		var dockerServices []DockerService
		dbMutex.RLock()
		err := db.Where("server_id = ?", serverID).Find(&dockerServices).Error
		dbMutex.RUnlock()
		if err != nil {
			log.Printf("Failed to get Docker services for ServerID %d: %v", serverID, err)
			http.Error(w, "Failed to get Docker services", http.StatusInternalServerError)
			return
		}
		for _, s := range dockerServices {
			services = append(services, Service{
				Name:        s.Name,
				Status:      s.Status,
				Description: s.Description,
				PID:         s.PID,
				Uptime:      s.Uptime,
				Memory:      s.Memory,
				CPU:         s.CPU,
				Type:        "docker",
			})
		}
	default:
		log.Printf("Invalid service type: %s", serviceType)
		errorMsg := fmt.Sprintf(`{"error": "Invalid service type: %s"}`, serviceType)
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(errorMsg))
		return
	}

	log.Printf("Successfully listed %d %s services for ServerID %d", len(services), serviceType, serverID)
	json.NewEncoder(w).Encode(services)
}

func handleControlServiceAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	// [Translated] 处理预检OPTIONS请求
	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}

	log.Printf("Service control request: method=%s, path=%s, content-type=%s",
		r.Method, r.URL.Path, r.Header.Get("Content-Type"))

	if r.Method != http.MethodPost {
		log.Printf("Invalid method for service control: %s", r.Method)
		errorMsg := fmt.Sprintf(`{"error": "Only POST method is allowed, got %s"}`, r.Method)
		w.WriteHeader(http.StatusMethodNotAllowed)
		w.Write([]byte(errorMsg))
		return
	}

	// [Translated] 读取原始请求体以进行日志记录
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("Failed to read request body: %v", err)
		errorMsg := `{"error": "Failed to read request body"}`
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(errorMsg))
		return
	}

	log.Printf("Raw request body: %s", string(bodyBytes))

	// [Translated] 解析JSON
	var req ServiceRequest
	err = json.Unmarshal(bodyBytes, &req)
	if err != nil {
		log.Printf("Failed to decode request body: %v", err)
		errorMsg := fmt.Sprintf(`{"error": "Invalid JSON format: %s"}`, err.Error())
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(errorMsg))
		return
	}

	// [Translated] 从URL路径中提取操作
	action := strings.TrimPrefix(r.URL.Path, "/api/services/"+req.ServiceType+"/")
	log.Printf("Parsed request: type=%s, name=%s, action=%s, serverID=%d",
		req.ServiceType, req.ServiceName, action, req.ServerID)

	// [Translated] 验证必填字段
	if req.ServiceName == "" {
		log.Printf("Missing serviceName field")
		errorMsg := `{"error": "serviceName is required"}`
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(errorMsg))
		return
	}

	if req.ServiceType == "" {
		log.Printf("Missing serviceType field")
		errorMsg := `{"error": "serviceType is required"}`
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(errorMsg))
		return
	}

	if action == "" {
		log.Printf("Missing action in URL path")
		errorMsg := `{"error": "action is required in URL path"}`
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(errorMsg))
		return
	}

	// [Translated] 执行服务控制
	err = controlService(req.ServiceType, req.ServiceName, action)
	if err != nil {
		log.Printf("Service control failed: %v", err)
		errorMsg := fmt.Sprintf(`{"error": "%s"}`, err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte(errorMsg))
		return
	}

	log.Printf("Service control successful: %s %s %s", req.ServiceType, action, req.ServiceName)
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"message": "Service operation successful"})
}

func handleGetServiceLogsAPI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain") // [Translated] 日志是纯文本
	w.Header().Set("Access-Control-Allow-Origin", "*")

	serviceType := r.URL.Query().Get("serviceType")
	serviceName := r.URL.Query().Get("serviceName")

	if serviceType == "" || serviceName == "" {
		http.Error(w, "serviceType and serviceName parameters are required", http.StatusBadRequest)
		return
	}

	logs, err := getServiceLogs(serviceType, serviceName)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Write([]byte(logs))
}

func connectAndMonitor(config *ClientConfig) {
	url := fmt.Sprintf("ws://%s:%s%s", config.Server, config.Port, config.WebSocket.Path)

	for {
		// fmt.Printf("Connecting to %s...\n", url) // Commented out to reduce client output

		conn, _, err := websocket.DefaultDialer.Dial(url, nil)
		if err != nil {
			log.Printf("Connection failed: %v", err)
			time.Sleep(5 * time.Second)
			continue
		}

		fmt.Println("Connected to server")

		// [Translated] 启动监控循环
		monitorLoop(conn, config.Password, config)

		conn.Close()
		// fmt.Println("Disconnected, retrying in 5 seconds...") // Commented out to reduce client output
		time.Sleep(5 * time.Second)
	}
}

func monitorLoop(conn *websocket.Conn, password string, config *ClientConfig) {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		serverDetails, supervisorServices, systemdServices, dockerServices, err := collectServerDetails(config)
		if err != nil {
			log.Printf("Failed to collect server details: %v", err)
			continue
		}

		// [Translated] 本地存储（创建不带固定ID的副本以进行本地存储）
		// [Translated] 注意：随着表的拆分，本地存储逻辑可能需要调整或删除
		// [Translated] 当前，我们将跳过本地存储以简化操作
		// localDetails := *serverDetails
		// localDetails.ID = 0 // [Translated] 让数据库自动生成本地存储的ID
		// if err := saveWithRetry(&localDetails, 3); err != nil {
		// 	log.Printf("Failed to save local details after retries: %v", err)
		// }

		// [Translated] 加密服务器详细信息
		serverDetailsJSON, err := json.Marshal(serverDetails)
		if err != nil {
			log.Printf("Failed to marshal server details: %v", err)
			continue
		}

		encryptedData, err := encrypt(serverDetailsJSON, password)
		if err != nil {
			log.Printf("Failed to encrypt data: %v", err)
			continue
		}

		// [Translated] 发送到服务器
		msg := Message{
			Type:               "system_info",
			Password:           password,
			EncryptedData:      encryptedData,
			Encrypted:          true,
			Timestamp:          time.Now(),
			SupervisorServices: supervisorServices,
			SystemdServices:    systemdServices,
			DockerServices:     dockerServices,
		}

		err = conn.WriteJSON(msg)
		if err != nil {
			log.Printf("Send error: %v", err)
			return
		}

		// fmt.Printf("Sent Server[%d:%s]: CPU=%.2f%%, Mem=%.2fGB\n",
		// 	serverDetails.ID, serverDetails.Name, serverDetails.Status.CPU,
		// 	float64(serverDetails.Status.MemUsed)/1024/1024/1024)
	}
}

func collectServerDetails(config *ClientConfig) (*ServerDetails, []Service, []Service, []Service, error) {
	serverDetails := &ServerDetails{
		ID:   config.MID, // [Translated] 使用配置中的MID进行服务器识别
		Name: config.ServerInfo.Name,
		Tag:  config.ServerInfo.Tag,
	}

	var supervisorServices []Service
	var systemdServices []Service
	var dockerServices []Service

	// [Translated] 如果启用，自动检测IP
	if config.ServerInfo.AutoDetectIP {
		// [Translated] 这将通过实际的IP检测逻辑实现
		serverDetails.IPv4 = "127.0.0.1" // [Translated] 占位符
		serverDetails.ValidIP = serverDetails.IPv4
	} else {
		serverDetails.IPv4 = config.ServerInfo.IPv4
		serverDetails.IPv6 = config.ServerInfo.IPv6
		if serverDetails.IPv4 != "" {
			serverDetails.ValidIP = serverDetails.IPv4
		} else {
			serverDetails.ValidIP = serverDetails.IPv6
		}
	}

	// [Translated] 获取主机信息
	hostInfo, err := host.Info()
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("failed to get host info: %v", err)
	}
	// log.Printf("Operating System: %s, Platform: %s, PlatformFamily: %s, PlatformVersion: %s",
	// 	hostInfo.OS, hostInfo.Platform, hostInfo.PlatformFamily, hostInfo.PlatformVersion)

	// [Translated] 获取CPU信息
	cpuInfo, err := cpu.Info()
	var cpuNames []string
	if err == nil && len(cpuInfo) > 0 {
		cpuNames = []string{cpuInfo[0].ModelName}
	} else {
		cpuNames = []string{"Unknown CPU"}
	}

	// [Translated] 获取内存信息
	memInfo, err := mem.VirtualMemory()
	if err != nil {
		log.Printf("Error getting memory info in collectServerDetails: %v", err)
		return nil, nil, nil, nil, fmt.Errorf("failed to get memory info: %v", err)
	}
	// log.Printf("Debug: collectServerDetails - MemTotal: %d", memInfo.Total)

	// [Translated] 获取磁盘信息
	// [Translated] 获取磁盘信息
	var diskTotal, diskUsed uint64
	if hostInfo.OS == "linux" {
		diskInfo, err := disk.Usage("/")
		if err != nil {
			log.Printf("Failed to get disk info for / on Linux: %v", err)
		} else {
			diskTotal = diskInfo.Total
			diskUsed = diskInfo.Used
		}
	} else if hostInfo.OS == "windows" {
		diskInfo, err := disk.Usage("C:")
		if err != nil {
			log.Printf("Failed to get disk info for C: on Windows: %v", err)
		} else {
			diskTotal = diskInfo.Total
			diskUsed = diskInfo.Used
		}
	} else {
		// log.Printf("Disk usage collection skipped for non-Linux/Windows OS: %s", hostInfo.OS)
	}
	// log.Printf("Debug: collectServerDetails - DiskTotal: %d, DiskUsed: %d", diskTotal, diskUsed)

	// [Translated] 将CPU名称转换为JSON字符串
	cpuJSON, _ := json.Marshal(cpuNames)

	// [Translated] 填充主机信息
	serverDetails.Host = HostInfo{
		Platform:        hostInfo.Platform,
		PlatformVersion: hostInfo.PlatformVersion,
		CPU:             string(cpuJSON),
		MemTotal:        memInfo.Total,
		DiskTotal:       diskTotal,
		SwapTotal:       memInfo.SwapTotal,
		Arch:            hostInfo.KernelArch,
		Virtualization:  hostInfo.VirtualizationSystem,
		BootTime:        int64(hostInfo.BootTime),
		CountryCode:     "auto", // [Translated] 将在实际实现中检测到
		Version:         hostInfo.KernelVersion,
	}

	// [Translated] 获取当前状态
	cpuPercent, err := cpu.Percent(time.Second, false)
	var cpuUsage float64
	if err == nil && len(cpuPercent) > 0 {
		cpuUsage = cpuPercent[0]
	}

	// [Translated] 获取平均负载
	loadInfo, err := load.Avg()
	var load1, load5, load15 float64
	if err == nil {
		load1 = loadInfo.Load1
		load5 = loadInfo.Load5
		load15 = loadInfo.Load15
	}

	// [Translated] 获取网络I/O
	netInfo, err := net.IOCounters(false)
	var netIn, netOut uint64
	if err == nil && len(netInfo) > 0 {
		netIn = netInfo[0].BytesRecv
		netOut = netInfo[0].BytesSent
	}

	// [Translated] 使用企业级跟踪计算网络速度
	netInSpeed, netOutSpeed := networkTracker.calculateNetworkSpeed(netIn, netOut)

	// [Translated] 获取交换信息
	swapInfo, _ := mem.SwapMemory()
	var swapUsed uint64
	if swapInfo != nil {
		swapUsed = swapInfo.Used
	}

	// [Translated] 填充状态信息
	serverDetails.Status = StatusInfo{
		CPU:            cpuUsage,
		MemUsed:        memInfo.Used,
		MemTotal:       memInfo.Total, // Added MemTotal
		SwapUsed:       swapUsed,
		DiskUsed:       diskUsed,
		DiskTotal:      diskTotal, // Add DiskTotal to StatusInfo
		NetInTransfer:  netIn,
		NetOutTransfer: netOut,
		NetInSpeed:     netInSpeed,  // [Translated] 实时下载速度
		NetOutSpeed:    netOutSpeed, // [Translated] 实时上传速度
		Uptime:         uint64(time.Now().Unix() - int64(hostInfo.BootTime)),
		Load1:          load1,
		Load5:          load5,
		Load15:         load15,
		TcpConnCount:   0, // [Translated] 需要额外实现
		UdpConnCount:   0, // [Translated] 需要额外实现
		ProcessCount:   int(hostInfo.Procs),
	}

	// [Translated] 收集Supervisor服务
	supServices, err := listSupervisorServices()
	if err != nil {
		if hostInfo.OS != "windows" { // Only log if not Windows
			log.Printf("Failed to list Supervisor services: %v", err)
		}
		// [Translated] 不返回错误，继续收集其他数据
	} else {
		supervisorServices = supServices
	}

	// [Translated] 收集Systemd服务
	sysdServices, err := listSystemdServices()
	if err != nil {
		if hostInfo.OS != "windows" { // Only log if not Windows
			log.Printf("Failed to list Systemd services: %v", err)
		}
		// [Translated] 不返回错误，继续收集其他数据
	} else {
		systemdServices = sysdServices
	}

	// [Translated] 收集Docker服务
	docServices, err := listDockerServices()
	if err != nil {
		if hostInfo.OS != "windows" { // Only log if not Windows
			log.Printf("Failed to list Docker services: %v", err)
		}
		// [Translated] 不返回错误，继续收集其他数据
	} else {
		dockerServices = docServices
	}

	return serverDetails, supervisorServices, systemdServices, dockerServices, nil
}

// [Translated] 加密函数
const (
	keySize    = 16    // [Translated] AES-128
	nonceSize  = 12    // [Translated] GCM nonce大小
	saltSize   = 16    // [Translated] PBKDF2盐大小
	iterations = 10000 // [Translated] PBKDF2迭代次数
)

// [Translated] deriveKey使用PBKDF2从密码派生密钥
func deriveKey(password string, salt []byte) []byte {
	return pbkdf2.Key([]byte(password), salt, iterations, keySize, sha256.New)
}

// [Translated] encrypt使用AES-128-GCM加密数据
func encrypt(data []byte, password string) (string, error) {
	// [Translated] 生成随机盐
	salt := make([]byte, saltSize)
	if _, err := rand.Read(salt); err != nil {
		return "", fmt.Errorf("failed to generate salt: %v", err)
	}

	// [Translated] 从密码派生密钥
	key := deriveKey(password, salt)

	// [Translated] 创建AES密码
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %v", err)
	}

	// [Translated] 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %v", err)
	}

	// [Translated] 生成随机nonce
	nonce := make([]byte, nonceSize)
	if _, err := rand.Read(nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %v", err)
	}

	// [Translated] 加密数据
	ciphertext := gcm.Seal(nil, nonce, data, nil)

	// [Translated] 组合盐 + nonce + 密文
	result := make([]byte, saltSize+nonceSize+len(ciphertext))
	copy(result[:saltSize], salt)
	copy(result[saltSize:saltSize+nonceSize], nonce)
	copy(result[saltSize+nonceSize:], ciphertext)

	// [Translated] 返回base64编码结果
	return base64.StdEncoding.EncodeToString(result), nil
}

// [Translated] decrypt使用AES-128-GCM解密数据
func decrypt(encryptedData string, password string) ([]byte, error) {
	// [Translated] 解码base64
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64: %v", err)
	}

	// [Translated] 检查最小长度
	if len(data) < saltSize+nonceSize {
		return nil, fmt.Errorf("encrypted data too short")
	}

	// [Translated] 提取盐、nonce和密文
	salt := data[:saltSize]
	nonce := data[saltSize : saltSize+nonceSize]
	ciphertext := data[saltSize+nonceSize:]

	// [Translated] 从密码派生密钥
	key := deriveKey(password, salt)

	// [Translated] 创建AES密码
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %v", err)
	}

	// [Translated] 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %v", err)
	}

	// [Translated] 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %v", err)
	}

	return plaintext, nil
}

// [Translated] listServices列出指定类型的服务
func listServices(serviceType string) ([]Service, error) {
	switch serviceType {
	case "supervisor":
		return listSupervisorServices()
	case "systemd":
		return listSystemdServices()
	case "docker":
		return listDockerServices()
	default:
		return nil, fmt.Errorf("invalid service type: %s", serviceType)
	}
}

// [Translated] controlService控制服务（启动、停止、重启）
func controlService(serviceType, serviceName, action string) error {
	switch serviceType {
	case "supervisor":
		return controlSupervisorService(serviceName, action)
	case "systemd":
		return controlSystemdService(serviceName, action)
	case "docker":
		return controlDockerService(serviceName, action)
	default:
		return fmt.Errorf("invalid service type: %s", serviceType)
	}
}

// [Translated] getServiceLogs获取服务的日志
func getServiceLogs(serviceType, serviceName string) (string, error) {
	switch serviceType {
	case "supervisor":
		return getSupervisorLogs(serviceName)
	case "systemd":
		return getSystemdLogs(serviceName)
	case "docker":
		return getDockerLogs(serviceName)
	default:
		return "", fmt.Errorf("invalid service type: %s", serviceType)
	}
}

// [Translated] executeCommand执行系统命令并返回输出
func executeCommand(command string, args ...string) (string, error) {
	cmd := exec.Command(command, args...)
	output, err := cmd.CombinedOutput()
	return string(output), err
}

// [Translated] Supervisor服务的辅助函数
func listSupervisorServices() ([]Service, error) {
	hostInfo, err := host.Info()
	if err == nil && hostInfo.OS == "windows" {
		return getMockSupervisorServices(), nil
	}

	// log.Printf("执行 supervisorctl status 命令...")
	output, err := executeCommand("supervisorctl", "status")
	if err != nil {
		// log.Printf("supervisorctl status 失败: %v, 输出: %s", err, output) // 添加输出到日志

		// 检查是否是ExitError且退出代码为3（某些程序已停止）
		if exitErr, ok := err.(*exec.ExitError); ok && exitErr.ExitCode() == 3 {
			// log.Printf("supervisorctl 以状态3退出（某些程序已停止），继续解析输出。")
			// 继续解析输出，不返回模拟数据
		} else if strings.Contains(err.Error(), "executable file not found") {
			// [Translated] 在Windows上，这是预期的，所以我们不记录它
			if hostInfo, err := host.Info(); err == nil && hostInfo.OS != "windows" {
				log.Printf("supervisorctl not found. Please install supervisor: sudo apt install supervisor")
			}
			return getMockSupervisorServices(), nil
		} else {
			// 对于任何其他Supervisor错误，返回模拟数据以防止500错误
			// log.Printf("supervisorctl 错误，返回模拟数据用于测试: %v", err)
			return getMockSupervisorServices(), nil
		}
	}

	// log.Printf("supervisorctl status 输出: %s", output)

	var services []Service
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) < 2 {
			// log.Printf("跳过此行: 行中部分不足 (%d): %s", len(parts), line)
			continue
		}

		name := parts[0]
		status := parts[1]
		description := ""
		pidStr := ""
		uptimeStr := ""

		// [Translated] supervisorctl status output format can vary.
		// [Translated] Example: "program_name                 RUNNING   pid 1234, uptime 0:00:05"
		// [Translated] Or: "program_name                 FATAL     Exited too quickly"
		if len(parts) > 2 {
			// [Translated] Check for "pid" and "uptime" keywords
			pidIndex := -1
			uptimeIndex := -1
			for i, part := range parts {
				if part == "pid" {
					pidIndex = i
				}
				if part == "uptime" {
					uptimeIndex = i
				}
			}

			if pidIndex != -1 && pidIndex+1 < len(parts) {
				pidStr = strings.TrimSuffix(parts[pidIndex+1], ",") // Remove trailing comma
			}
			if uptimeIndex != -1 && uptimeIndex+1 < len(parts) {
				uptimeStr = strings.Join(parts[uptimeIndex+1:], " ")
			}

			// [Translated] If "pid" and "uptime" are present, description is before "pid"
			if pidIndex != -1 {
				description = strings.Join(parts[2:pidIndex], " ")
			} else {
				// [Translated] Otherwise, the rest of the line is description
				description = strings.Join(parts[2:], " ")
			}
		}

		service := Service{
			Name:        name,
			Status:      strings.ToLower(status),
			Description: description,
			PID:         pidStr,
			Uptime:      parseUptimeStringToSeconds(uptimeStr), // Convert string to int64 seconds
			Type:        "supervisor",
		}

		// [Translated] 获取进程的CPU和内存使用情况
		if service.PID != "" {
			pid, err := strconv.ParseInt(service.PID, 10, 32)
			if err == nil {
				p, err := proc.NewProcess(int32(pid)) // Use the alias 'proc'
				if err == nil {
					cpuPercent, err := p.CPUPercent()
					if err == nil {
						service.CPU = fmt.Sprintf("%.2f%%", cpuPercent)
					} else {
						log.Printf("Failed to get CPU for PID %d: %v", pid, err)
						service.CPU = "--" // Set to -- if failed
					}

					memInfo, err := p.MemoryInfo()
					if err == nil {
						service.Memory = formatBytes(memInfo.RSS)
					} else {
						log.Printf("Failed to get Memory for PID %d: %v", pid, err)
						service.Memory = "--" // Set to -- if failed
					}
				} else {
					log.Printf("Failed to create process for PID %d: %v", pid, err)
				}
			} else {
				log.Printf("Failed to parse PID '%s': %v", service.PID, err)
			}
		}

		// log.Printf("Parsed service: %+v", service)
		services = append(services, service)
	}

	// log.Printf("成功解析 %d 个 supervisor 服务", len(services))
	return services, nil
}

// [Translated] 格式化字节为人类可读的单位
func formatBytes(b uint64) string {
	const unit = 1024
	if b < unit {
		return fmt.Sprintf("%d B", b)
	}
	div, exp := uint64(unit), 0
	for n := b / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(b)/float64(div), "KMGTPE"[exp])
}

// 获取模拟的 Supervisor 服务数据（用于测试）
func getMockSupervisorServices() []Service {
	return []Service{
		{
			Name:        "xr-hertz:xr-hertz_00",
			Status:      "running",
			Description: "Hertz web service instance 00",
			PID:         "1234",
			Uptime:      parseUptimeStringToSeconds("2 days, 14:30:25"), // Convert string to int64 seconds
			Memory:      "128 MB",                                       // Added mock memory
			CPU:         "5.23%",                                        // Added mock CPU
			Type:        "supervisor",
		},
		{
			Name:        "xr-hertz:xr-hertz_01",
			Status:      "stopped",
			Description: "Hertz web service instance 01",
			PID:         "",
			Uptime:      0,    // Stopped services have 0 uptime
			Memory:      "--", // Added mock memory
			CPU:         "--", // Added mock CPU
			Type:        "supervisor",
		},
		{
			Name:        "nginx-proxy",
			Status:      "running",
			Description: "Nginx reverse proxy",
			PID:         "5678",
			Uptime:      parseUptimeStringToSeconds("5 days, 08:15:42"), // Convert string to int64 seconds
			Memory:      "64 MB",                                        // Added mock memory
			CPU:         "1.50%",                                        // Added mock CPU
			Type:        "supervisor",
		},
		{
			Name:        "redis-server",
			Status:      "running",
			Description: "Redis cache server",
			PID:         "9012",
			Uptime:      parseUptimeStringToSeconds("3 days, 12:45:18"), // Convert string to int64 seconds
			Memory:      "256 MB",                                       // Added mock memory
			CPU:         "3.10%",                                        // Added mock CPU
			Type:        "supervisor",
		},
		{
			Name:        "celery-worker",
			Status:      "failed",
			Description: "Celery background worker",
			PID:         "",
			Uptime:      0,    // Failed services have 0 uptime
			Memory:      "--", // Added mock memory
			CPU:         "--", // Added mock CPU
			Type:        "supervisor",
		},
	}
}

func controlSupervisorService(serviceName, action string) error {
	output, err := executeCommand("supervisorctl", action, serviceName)
	if err != nil {
		errorMessage := fmt.Sprintf("supervisorctl %s %s failed: %v, output: %s", action, serviceName, err, output)
		log.Printf("Error controlling Supervisor service: %s", errorMessage)

		// [Translated] 提供更具体的错误提示
		if strings.Contains(output, "ERROR (no such process)") || strings.Contains(output, "No such process") {
			errorMessage += "\n\nPossible reasons:\n- The service name might be incorrect. Please check the exact program name in your Supervisor configuration (e.g., /etc/supervisor/conf.d/).\n- The service is not configured or managed by Supervisor.\n- The service is not running and cannot be stopped/restarted if it's already down."
		} else if strings.Contains(err.Error(), "executable file not found") {
			errorMessage += "\n\nPossible reasons:\n- 'supervisorctl' command not found. Please ensure Supervisor is installed and in your system's PATH."
		} else if strings.Contains(err.Error(), "exit status") {
			errorMessage += "\n\nPossible reasons:\n- Supervisor daemon might not be running (try: sudo systemctl start supervisor).\n- Insufficient permissions to execute 'supervisorctl' (try: sudo supervisorctl [action] [service_name])."
		}
		return fmt.Errorf("%s", errorMessage)
	}
	return nil
}

func getSupervisorLogs(serviceName string) (string, error) {
	output, err := executeCommand("supervisorctl", "tail", serviceName)
	if err != nil {
		// [Translated] 处理supervisor未找到或其他错误
		if strings.Contains(err.Error(), "executable file not found") {
			log.Printf("supervisorctl not found for logs. Please install supervisor: sudo apt install supervisor")
			return "supervisorctl not found. Please install supervisor on this system.\n\nOn Debian/Ubuntu: sudo apt install supervisor\nOn CentOS/RHEL: sudo yum install supervisor", nil
		}

		// [Translated] 处理其他supervisor错误
		if strings.Contains(err.Error(), "exit status") {
			log.Printf("supervisorctl tail failed for %s: %v", serviceName, err)
			return fmt.Sprintf("Failed to get logs for service '%s'.\n\nPossible issues:\n- Supervisor daemon not running (try: sudo systemctl start supervisor)\n- Service name incorrect\n- Insufficient permissions\n\nError: %v", serviceName, err), nil
		}

		return "", err
	}
	return output, nil
}

// [Translated] Systemd服务的辅助函数
func listSystemdServices() ([]Service, error) {
	hostInfo, err := host.Info()
	if err == nil && hostInfo.OS == "windows" {
		return getMockSystemdServices(), nil
	}
	output, err := executeCommand("systemctl", "list-units", "--type=service", "--all", "--no-legend", "--plain")
	if err != nil {
		if strings.Contains(err.Error(), "executable file not found") {
			// [Translated] 在Windows上，这是预期的，所以我们不记录它
			if hostInfo, err := host.Info(); err == nil && hostInfo.OS != "windows" {
				log.Printf("systemctl not found. Please install systemd")
			}
			return getMockSystemdServices(), nil
		}
		return nil, fmt.Errorf("failed to execute systemctl list-units: %w", err)
	}

	var services []Service
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) < 4 {
			continue
		}

		name := parts[0]
		activeStatus := parts[2]
		subStatus := parts[3]

		status := "unknown"
		if activeStatus == "active" && subStatus == "running" {
			status = "running"
		} else if activeStatus == "active" && subStatus == "exited" {
			status = "stopped"
		} else if activeStatus == "inactive" || subStatus == "dead" {
			status = "stopped"
		} else if activeStatus == "failed" {
			status = "failed"
		}

		description := strings.Join(parts[4:], " ")

		service := Service{
			Name:        name,
			Status:      status,
			Description: description,
			Type:        "systemd",
		}

		// [Translated] 获取Systemd服务的详细信息，包括PID和运行时间
		detailOutput, err := executeCommand("systemctl", "show", service.Name)
		if err != nil {
			log.Printf("Failed to get systemctl show for %s: %v", service.Name, err)
			// [Translated] 继续处理下一个服务，即使无法获取详细信息
		} else {
			// log.Printf("systemctl show %s 的输出: %s", service.Name, detailOutput) // 添加此日志
			pidStr := parseSystemdProperty(detailOutput, "MainPID")
			// log.Printf("解析 %s 的 PID: %s", service.Name, pidStr) // 添加此日志
			if pidStr != "" {
				pid, err := strconv.ParseInt(pidStr, 10, 32)
				if err == nil && pid > 0 {
					service.PID = pidStr
					p, err := proc.NewProcess(int32(pid))
					if err == nil {
						// [Translated] 获取CPU使用率
						cpuPercent, err := p.CPUPercent()
						if err == nil {
							service.CPU = fmt.Sprintf("%.2f%%", cpuPercent)
						} else {
							log.Printf("Failed to get CPU for PID %d: %v", pid, err)
							service.CPU = "--" // Set to -- if failed
						}

						// [Translated] 获取内存使用情况 (RSS)
						memInfo, err := p.MemoryInfo()
						if err == nil {
							service.Memory = formatBytes(memInfo.RSS)
						} else {
							log.Printf("Failed to get Memory for PID %d: %v", pid, err)
							service.Memory = "--" // Set to -- if failed
						}

						// [Translated] 获取运行时间
						createTime, err := p.CreateTime() // [Translated] 进程创建时间（Unix毫秒）
						if err == nil {
							uptimeSeconds := (time.Now().UnixNano() - createTime*1_000_000) / 1_000_000_000
							service.Uptime = uptimeSeconds
						} else {
							log.Printf("Failed to get CreateTime for PID %d: %v", pid, err)
						}

					} else {
						log.Printf("Failed to create process for PID %d: %v", pid, err)
					}
				} else {
					// log.Printf("服务 %s 的 PID %s 无效", pidStr, service.Name)
				}
			} else {
				// log.Printf("服务 %s 的 MainPID 未找到", service.Name)
			}
		}
		services = append(services, service)
	}
	return services, nil
}

// [Translated] 从systemctl show输出中解析属性值
func parseSystemdProperty(output, property string) string {
	prefix := property + "="
	for _, line := range strings.Split(output, "\n") {
		if strings.HasPrefix(line, prefix) {
			return strings.TrimSpace(strings.TrimPrefix(line, prefix))
		}
	}
	return ""
}

// [Translated] 格式化秒数为人类可读的运行时间
func formatUptime(seconds int64) string {
	if seconds < 0 {
		return "--"
	}
	days := seconds / (60 * 60 * 24)
	hours := (seconds % (60 * 60 * 24)) / (60 * 60)
	minutes := (seconds % (60 * 60)) / 60
	secs := seconds % 60

	parts := []string{}
	if days > 0 {
		parts = append(parts, fmt.Sprintf("%d days", days))
	}
	if hours > 0 {
		parts = append(parts, fmt.Sprintf("%d hours", hours))
	}
	if minutes > 0 {
		parts = append(parts, fmt.Sprintf("%d minutes", minutes))
	}
	if secs > 0 || len(parts) == 0 { // Always show seconds if no larger unit, or if it's 0 seconds
		parts = append(parts, fmt.Sprintf("%d seconds", secs))
	}
	return strings.Join(parts, ", ")
}

// parseUptimeStringToSeconds parses a supervisorctl uptime string (e.g., "5 days, 16:11:58" or "0:00:51") into seconds.
func parseUptimeStringToSeconds(uptimeStr string) int64 {
	var totalSeconds int64

	// Handle "days" part
	if strings.Contains(uptimeStr, "days") {
		parts := strings.Split(uptimeStr, "days,")
		if len(parts) == 2 {
			daysStr := strings.TrimSpace(parts[0])
			days, err := strconv.ParseInt(daysStr, 10, 64)
			if err == nil {
				totalSeconds += days * 24 * 60 * 60
			}
			uptimeStr = strings.TrimSpace(parts[1]) // Remaining part for HH:MM:SS
		}
	} else if strings.Contains(uptimeStr, "day") { // Handle "day" (singular)
		parts := strings.Split(uptimeStr, "day,")
		if len(parts) == 2 {
			daysStr := strings.TrimSpace(parts[0])
			days, err := strconv.ParseInt(daysStr, 10, 64)
			if err == nil {
				totalSeconds += days * 24 * 60 * 60
			}
			uptimeStr = strings.TrimSpace(parts[1])
		}
	}

	// Handle HH:MM:SS or MM:SS or SS part
	timeParts := strings.Split(uptimeStr, ":")
	var hours, minutes, seconds int64
	var err error

	if len(timeParts) == 3 { // HH:MM:SS
		hours, err = strconv.ParseInt(strings.TrimSpace(timeParts[0]), 10, 64)
		if err != nil {
			return 0
		}
		minutes, err = strconv.ParseInt(strings.TrimSpace(timeParts[1]), 10, 64)
		if err != nil {
			return 0
		}
		seconds, err = strconv.ParseInt(strings.TrimSpace(timeParts[2]), 10, 64)
		if err != nil {
			return 0
		}
	} else if len(timeParts) == 2 { // MM:SS (common for shorter uptimes)
		minutes, err = strconv.ParseInt(strings.TrimSpace(timeParts[0]), 10, 64)
		if err != nil {
			return 0
		}
		seconds, err = strconv.ParseInt(strings.TrimSpace(timeParts[1]), 10, 64)
		if err != nil {
			return 0
		}
	} else if len(timeParts) == 1 { // SS (very short uptimes)
		// Check if it's just seconds (e.g., "51 seconds")
		sParts := strings.Fields(uptimeStr)
		if len(sParts) == 2 && sParts[1] == "seconds" {
			seconds, err = strconv.ParseInt(strings.TrimSpace(sParts[0]), 10, 64)
			if err != nil {
				return 0
			}
		} else {
			// If it's just a number, assume it's seconds
			seconds, err = strconv.ParseInt(strings.TrimSpace(timeParts[0]), 10, 64)
			if err != nil {
				return 0
			}
		}
	} else {
		return 0 // Unknown format
	}

	totalSeconds += hours*60*60 + minutes*60 + seconds
	return totalSeconds
}

// 获取模拟的 Systemd 服务数据（用于测试）
func getMockSystemdServices() []Service {
	return []Service{
		{
			Name:        "nginx.service",
			Status:      "running",
			Description: "A high performance web server and a reverse proxy server",
			PID:         "12345",
			Uptime:      parseUptimeStringToSeconds("1 day, 5 hours, 30 minutes, 15 seconds"),
			Memory:      "64 MB",
			CPU:         "3.50%",
			Type:        "systemd",
		},
		{
			Name:        "mysql.service",
			Status:      "running",
			Description: "MySQL Community Server",
			PID:         "67890",
			Uptime:      parseUptimeStringToSeconds("2 days, 10 hours, 0 minutes, 0 seconds"),
			Memory:      "256 MB",
			CPU:         "8.10%",
			Type:        "systemd",
		},
		{
			Name:        "redis.service",
			Status:      "running",
			Description: "Advanced key-value store",
			PID:         "11223",
			Uptime:      parseUptimeStringToSeconds("12 hours, 45 minutes, 30 seconds"),
			Memory:      "32 MB",
			CPU:         "1.20%",
			Type:        "systemd",
		},
		{
			Name:        "postgresql.service",
			Status:      "stopped",
			Description: "PostgreSQL database server",
			PID:         "",
			Uptime:      0, // Stopped services have 0 uptime
			Memory:      "--",
			CPU:         "--",
			Type:        "systemd",
		},
		{
			Name:        "apache2.service",
			Status:      "stopped",
			Description: "The Apache HTTP Server",
			PID:         "",
			Uptime:      0, // Stopped services have 0 uptime
			Memory:      "--",
			CPU:         "--",
			Type:        "systemd",
		},
		{
			Name:        "docker.service",
			Status:      "running",
			Description: "Docker Application Container Engine",
			PID:         "98765",
			Uptime:      parseUptimeStringToSeconds("3 days, 2 hours, 10 minutes, 5 seconds"),
			Memory:      "128 MB",
			CPU:         "6.70%",
			Type:        "systemd",
		},
		{
			Name:        "ssh.service",
			Status:      "running",
			Description: "OpenBSD Secure Shell server",
			PID:         "54321",
			Uptime:      parseUptimeStringToSeconds("1 day, 0 hours, 0 minutes, 0 seconds"),
			Memory:      "10 MB",
			CPU:         "0.10%",
			Type:        "systemd",
		},
		{
			Name:        "fail2ban.service",
			Status:      "failed",
			Description: "Fail2Ban Service",
			PID:         "",
			Uptime:      0, // Failed services have 0 uptime
			Memory:      "--",
			CPU:         "--",
			Type:        "systemd",
		},
		{
			Name:        "elasticsearch.service",
			Status:      "stopped",
			Description: "Elasticsearch",
			PID:         "",
			Uptime:      0, // Stopped services have 0 uptime
			Memory:      "--",
			CPU:         "--",
			Type:        "systemd",
		},
		{
			Name:        "kibana.service",
			Status:      "stopped",
			Description: "Kibana",
			PID:         "",
			Uptime:      0, // Stopped services have 0 uptime
			Memory:      "--",
			CPU:         "--",
			Type:        "systemd",
		},
	}
}

func controlSystemdService(serviceName, action string) error {
	output, err := executeCommand("systemctl", action, serviceName)
	if err != nil {
		return fmt.Errorf("systemctl %s %s failed: %w, output: %s", action, serviceName, err, output)
	}
	return nil
}

func getSystemdLogs(serviceName string) (string, error) {
	output, err := executeCommand("journalctl", "-u", serviceName, "--no-pager", "-n", "100")
	if err != nil {
		// [Translated] 处理journalctl未找到或其他错误
		if strings.Contains(err.Error(), "executable file not found") {
			log.Printf("journalctl not found for logs. This system may not use systemd")
			return "journalctl not found. This system may not use systemd.\n\nSystemd is typically available on:\n- Modern Linux distributions (Ubuntu 15.04+, CentOS 7+, Debian 8+)\n- Not available on Windows or older Linux systems", nil
		}

		// [Translated] 处理其他systemd错误
		if strings.Contains(err.Error(), "exit status") {
			log.Printf("journalctl failed for %s: %v", serviceName, err)
			return fmt.Sprintf("Failed to get logs for service '%s'.\n\nPossible issues:\n- Service name incorrect\n- Insufficient permissions (try: sudo journalctl -u %s)\n- Service not managed by systemd\n\nError: %v", serviceName, serviceName, err), nil
		}

		return "", err
	}
	return output, nil
}

// [Translated] Docker服务的辅助函数
func listDockerServices() ([]Service, error) {
	hostInfo, err := host.Info()
	if err == nil && hostInfo.OS == "windows" {
		return getMockDockerServices(), nil
	}
	output, err := executeCommand("docker", "ps", "-a", "--format", "{{.ID}}\t{{.Names}}\t{{.Status}}")
	if err != nil {
		// [Translated] 在 Windows 环境下提供模拟数据用于测试
		if strings.Contains(err.Error(), "executable file not found") {
			// [Translated] 在Windows上，这是预期的，所以我们不记录它
			if hostInfo, err := host.Info(); err == nil && hostInfo.OS != "windows" {
				log.Printf("docker not found. Please install docker")
			}
			return getMockDockerServices(), nil
		}
		return nil, fmt.Errorf("failed to execute docker ps: %w", err)
	}

	var services []Service
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.Split(line, "\t")
		if len(parts) < 3 { // Updated to 3 parts: ID, Name, Status
			continue
		}

		containerID := parts[0]
		name := parts[1]
		rawStatus := parts[2]

		status := "unknown"
		if strings.HasPrefix(rawStatus, "Up") {
			status = "running"
		} else if strings.HasPrefix(rawStatus, "Exited") || strings.HasPrefix(rawStatus, "Created") {
			status = "stopped"
		}

		service := Service{
			Name:        name,
			Status:      status,
			Description: rawStatus, // Keep raw status for description
			Type:        "docker",
		}

		// [Translated] 获取Docker容器的详细信息，包括PID和运行时间
		// [Translated] 从rawStatus中解析Uptime
		service.Uptime = parseDockerUptimeToSeconds(rawStatus)

		inspectOutput, err := executeCommand("docker", "inspect", containerID)
		if err != nil {
			log.Printf("Failed to inspect docker container %s: %v", containerID, err)
			// [Translated] 继续处理下一个服务，即使无法获取详细信息
		} else {
			var inspectData []struct {
				State struct {
					Pid int `json:"Pid"`
				} `json:"State"`
			}
			err := json.Unmarshal([]byte(inspectOutput), &inspectData)
			if err != nil || len(inspectData) == 0 {
				log.Printf("Failed to parse docker inspect output for %s: %v", containerID, err)
			} else {
				pid := inspectData[0].State.Pid
				if pid > 0 {
					service.PID = fmt.Sprintf("%d", pid)
					p, err := proc.NewProcess(int32(pid))
					if err == nil {
						// [Translated] 获取CPU使用率
						cpuPercent, err := p.CPUPercent()
						if err == nil {
							service.CPU = fmt.Sprintf("%.2f%%", cpuPercent)
						} else {
							log.Printf("Failed to get CPU for PID %d: %v", pid, err)
							service.CPU = "--" // Set to -- if failed
						}

						// [Translated] 获取内存使用情况 (RSS)
						memInfo, err := p.MemoryInfo()
						if err == nil {
							service.Memory = formatBytes(memInfo.RSS)
						} else {
							log.Printf("Failed to get Memory for PID %d: %v", pid, err)
							service.Memory = "--" // Set to -- if failed
						}
					} else {
						log.Printf("Failed to create process for PID %d: %v", pid, err)
					}
				} else {
					// log.Printf("容器 %s 的 PID 未找到", containerID)
				}
			}
		}
		services = append(services, service)
	}
	return services, nil
}

// 获取模拟的 Docker 服务数据（用于测试）
func getMockDockerServices() []Service {
	return []Service{
		{
			Name:        "web-app",
			Status:      "running",
			Description: "Up 2 hours",
			PID:         "12345",
			Uptime:      parseUptimeStringToSeconds("2 hours, 30 minutes, 15 seconds"),
			Memory:      "128 MB",
			CPU:         "3.50%",
			Type:        "docker",
		},
		{
			Name:        "redis-cache",
			Status:      "running",
			Description: "Up 5 days",
			PID:         "67890",
			Uptime:      parseUptimeStringToSeconds("5 days, 10 hours, 0 minutes, 0 seconds"),
			Memory:      "256 MB",
			CPU:         "8.10%",
			Type:        "docker",
		},
		{
			Name:        "postgres-db",
			Status:      "running",
			Description: "Up 3 days",
			PID:         "11223",
			Uptime:      parseUptimeStringToSeconds("3 days, 12 hours, 45 minutes, 30 seconds"),
			Memory:      "32 MB",
			CPU:         "1.20%",
			Type:        "docker",
		},
		{
			Name:        "nginx-proxy",
			Status:      "running",
			Description: "Up 1 week",
			PID:         "98765",
			Uptime:      parseUptimeStringToSeconds("7 days, 2 hours, 10 minutes, 5 seconds"),
			Memory:      "64 MB",
			CPU:         "6.70%",
			Type:        "docker",
		},
		{
			Name:        "monitoring-grafana",
			Status:      "stopped",
			Description: "Exited (0) 2 hours ago",
			PID:         "",
			Uptime:      0, // Stopped services have 0 uptime
			Memory:      "--",
			CPU:         "--",
			Type:        "docker",
		},
		{
			Name:        "log-collector",
			Status:      "stopped",
			Description: "Exited (1) 30 minutes ago",
			PID:         "",
			Uptime:      0, // Stopped services have 0 uptime
			Memory:      "--",
			CPU:         "--",
			Type:        "docker",
		},
	}
}

func parseDockerUptimeToSeconds(status string) int64 {
	// Example status: "Up 22 minutes", "Up 5 days", "Exited (0) 2 hours ago"
	if !strings.HasPrefix(status, "Up ") {
		return 0 // Not a running container, or uptime not available in this format
	}

	status = strings.TrimPrefix(status, "Up ")
	parts := strings.Fields(status)

	if len(parts) < 2 {
		return 0 // Not enough parts to parse (e.g., just "Up")
	}

	valueStr := parts[0]
	unit := parts[1]

	value, err := strconv.ParseInt(valueStr, 10, 64)
	if err != nil {
		log.Printf("Failed to parse uptime value '%s': %v", valueStr, err)
		return 0
	}

	switch {
	case strings.HasPrefix(unit, "second"):
		return value
	case strings.HasPrefix(unit, "minute"):
		return value * 60
	case strings.HasPrefix(unit, "hour"):
		return value * 60 * 60
	case strings.HasPrefix(unit, "day"):
		return value * 24 * 60 * 60
	case strings.HasPrefix(unit, "week"):
		return value * 7 * 24 * 60 * 60
	case strings.HasPrefix(unit, "month"): // Approximate
		return value * 30 * 24 * 60 * 60
	case strings.HasPrefix(unit, "year"): // Approximate
		return value * 365 * 24 * 60 * 60
	default:
		log.Printf("Unknown uptime unit '%s' in status: %s", unit, status)
		return 0
	}
}

func controlDockerService(serviceName, action string) error {
	switch action {
	case "start":
		output, err := executeCommand("docker", "start", serviceName)
		if err != nil {
			return fmt.Errorf("docker start %s failed: %w, output: %s", serviceName, err, output)
		}
		return nil
	case "stop":
		output, err := executeCommand("docker", "stop", serviceName)
		if err != nil {
			return fmt.Errorf("docker stop %s failed: %w, output: %s", serviceName, err, output)
		}
		return nil
	case "restart":
		output, err := executeCommand("docker", "restart", serviceName)
		if err != nil {
			return fmt.Errorf("docker restart %s failed: %w, output: %s", serviceName, err, output)
		}
		return nil
	default:
		return fmt.Errorf("invalid action: %s", action)
	}
}

func getDockerLogs(serviceName string) (string, error) {
	output, err := executeCommand("docker", "logs", "--tail", "100", serviceName)
	if err != nil {
		// [Translated] 处理docker未找到或其他错误
		if strings.Contains(err.Error(), "executable file not found") {
			log.Printf("docker not found for logs. Please install Docker")
			return "Docker not found. Please install Docker on this system.\n\nInstallation guides:\n- Ubuntu/Debian: sudo apt install docker.io\n- CentOS/RHEL: sudo yum install docker\n- Windows: Download Docker Desktop", nil
		}

		// [Translated] 处理其他docker错误
		if strings.Contains(err.Error(), "exit status") || strings.Contains(err.Error(), "No such container") {
			log.Printf("docker logs failed for %s: %v", serviceName, err)
			return fmt.Sprintf("Failed to get logs for container '%s'.\n\nPossible issues:\n- Container name incorrect\n- Container doesn't exist (check: docker ps -a)\n- Docker daemon not running (try: sudo systemctl start docker)\n- Insufficient permissions (try: sudo docker logs %s)\n\nError: %v", serviceName, serviceName, err), nil
		}

		return "", err
	}
	return output, nil
}

// [Translated] 连接管理器方法
func (cm *ConnectionManager) AddConnection(connID string, conn *websocket.Conn, userID string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.connections[connID] = &FrontendConnection{
		Conn:         conn,
		UserID:       userID,
		SubscribedTo: make(map[int]bool),
		LastPing:     time.Now(),
	}
}

func (cm *ConnectionManager) RemoveConnection(connID string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if conn, exists := cm.connections[connID]; exists {
		conn.Conn.Close()
		delete(cm.connections, connID)
	}
}

func (cm *ConnectionManager) Subscribe(connID string, serverID int) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if conn, exists := cm.connections[connID]; exists {
		conn.SubscribedTo[serverID] = true
	}
}

func (cm *ConnectionManager) Unsubscribe(connID string, serverID int) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if conn, exists := cm.connections[connID]; exists {
		delete(conn.SubscribedTo, serverID)
	}
}

func (cm *ConnectionManager) BroadcastToSubscribers(serverID int, message FrontendWSMessage) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	for _, conn := range cm.connections {
		if conn.SubscribedTo[serverID] {
			err := conn.Conn.WriteJSON(message)
			if err != nil {
				log.Printf("Failed to send message to connection: %v", err)
			}
		}
	}
}

func (cm *ConnectionManager) SendToConnection(connID string, message FrontendWSMessage) error {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if conn, exists := cm.connections[connID]; exists {
		return conn.Conn.WriteJSON(message)
	}
	return fmt.Errorf("connection not found")
}

// [Translated] 前端WebSocket处理程序
func handleFrontendWebSocket(w http.ResponseWriter, r *http.Request) {
	// [Translated] 在WebSocket升级前验证认证
	var tokenString string

	// [Translated] 优先从cookie读取token
	if cookie, err := r.Cookie("authToken"); err == nil && cookie.Value != "" {
		tokenString = cookie.Value
	} else {
		// [Translated] 向后兼容：从Authorization header读取token
		authHeader := r.Header.Get("Authorization")
		if authHeader != "" {
			tokenString = strings.TrimPrefix(authHeader, "Bearer ")
			if tokenString == authHeader {
				log.Printf("Invalid authorization format in WebSocket upgrade")
				http.Error(w, "Unauthorized", http.StatusUnauthorized)
				return
			}
		}
	}

	// [Translated] 验证令牌
	if tokenString != "" {
		_, err := validateJWT(tokenString)
		if err != nil {
			log.Printf("Invalid token in WebSocket upgrade: %v", err)
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}
	} else {
		log.Printf("No valid token found in WebSocket upgrade")
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("Frontend WebSocket upgrade failed: %v", err)
		return
	}

	connID := fmt.Sprintf("%s_%d", r.RemoteAddr, time.Now().UnixNano())
	log.Printf("Frontend client connected: %s (ID: %s)", r.RemoteAddr, connID)

	// [Translated] 处理连接清理
	defer func() {
		frontendConnManager.RemoveConnection(connID)
		log.Printf("Frontend client disconnected: %s", connID)
	}()

	// [Translated] 处理传入消息
	for {
		var msg FrontendWSMessage
		err := conn.ReadJSON(&msg)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("Frontend WebSocket error: %v", err)
			}
			break
		}

		// [Translated] 处理不同消息类型
		switch msg.Type {
		case "auth":
			handleFrontendAuth(connID, conn, msg)
		case "subscribe":
			handleFrontendSubscribe(connID, msg)
		case "unsubscribe":
			handleFrontendUnsubscribe(connID, msg)
		case "get_system_stats":
			handleFrontendGetSystemStats(connID, msg)
		case "get_service_list":
			handleFrontendGetServiceList(connID, msg)
		case "service_control":
			handleFrontendServiceControl(connID, msg)
		case "ping":
			handleFrontendPing(connID)
		default:
			log.Printf("Unknown message type: %s", msg.Type)
		}
	}
}

// [Translated] 前端WebSocket消息处理程序
func handleFrontendAuth(connID string, conn *websocket.Conn, msg FrontendWSMessage) {
	// [Translated] 由于前端不再发送token，我们假设WebSocket连接已经通过HTTP认证
	// 在WebSocket升级时，服务器已经验证了cookie
	// 这里我们直接接受认证，因为WebSocket连接本身已经建立了

	// [Translated] 添加已认证连接
	frontendConnManager.AddConnection(connID, conn, "authenticated_user")

	response := FrontendWSMessage{
		Type:      "auth_response",
		Data:      map[string]string{"status": "authenticated", "user": "authenticated_user"},
		Timestamp: time.Now(),
	}
	conn.WriteJSON(response)
}

func handleFrontendSubscribe(connID string, msg FrontendWSMessage) {
	if msg.ServerID > 0 {
		frontendConnManager.Subscribe(connID, msg.ServerID)

		response := FrontendWSMessage{
			Type:      "subscribe_response",
			ServerID:  msg.ServerID,
			Data:      map[string]string{"status": "subscribed"},
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
	}
}

func handleFrontendUnsubscribe(connID string, msg FrontendWSMessage) {
	if msg.ServerID > 0 {
		frontendConnManager.Unsubscribe(connID, msg.ServerID)

		response := FrontendWSMessage{
			Type:      "unsubscribe_response",
			ServerID:  msg.ServerID,
			Data:      map[string]string{"status": "unsubscribed"},
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
	}
}

func handleFrontendGetSystemStats(connID string, msg FrontendWSMessage) {
	// [Translated] 获取当前系统统计信息
	stats, err := getCurrentSystemStats()
	if err != nil {
		response := FrontendWSMessage{
			Type:      "system_stats_response",
			Error:     err.Error(),
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
		return
	}

	response := FrontendWSMessage{
		Type:      "system_stats_response",
		Data:      stats,
		Timestamp: time.Now(),
	}
	frontendConnManager.SendToConnection(connID, response)
}

func handleFrontendGetServiceList(connID string, msg FrontendWSMessage) {
	// [Translated] 从消息数据中提取服务类型
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		response := FrontendWSMessage{
			Type:      "service_list_response",
			Error:     "Invalid request data",
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
		return
	}

	serviceType, ok := data["serviceType"].(string)
	if !ok {
		response := FrontendWSMessage{
			Type:      "service_list_response",
			Error:     "Missing serviceType",
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
		return
	}

	var services []Service
	var err error

	switch serviceType {
	case "supervisor":
		services, err = listSupervisorServices()
	case "systemd":
		services, err = listSystemdServices()
	case "docker":
		services, err = listDockerServices()
	default:
		err = fmt.Errorf("invalid service type: %s", serviceType)
	}

	if err != nil {
		response := FrontendWSMessage{
			Type:      "service_list_response",
			Error:     err.Error(),
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
		return
	}

	response := FrontendWSMessage{
		Type:      "service_list_response",
		Data:      map[string]interface{}{"services": services, "serviceType": serviceType},
		Timestamp: time.Now(),
	}
	frontendConnManager.SendToConnection(connID, response)
}

func handleFrontendServiceControl(connID string, msg FrontendWSMessage) {
	// [Translated] 从消息中提取服务控制数据
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		response := FrontendWSMessage{
			Type:      "service_control_response",
			Error:     "Invalid request data",
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
		return
	}

	serviceType, _ := data["serviceType"].(string)
	serviceName, _ := data["serviceName"].(string)
	action, _ := data["action"].(string)

	if serviceType == "" || serviceName == "" || action == "" {
		response := FrontendWSMessage{
			Type:      "service_control_response",
			Error:     "Missing required fields: serviceType, serviceName, action",
			Timestamp: time.Now(),
		}
		frontendConnManager.SendToConnection(connID, response)
		return
	}

	var err error
	switch serviceType {
	case "supervisor":
		err = controlSupervisorService(serviceName, action)
	case "systemd":
		err = controlSystemdService(serviceName, action)
	case "docker":
		err = controlDockerService(serviceName, action)
	default:
		err = fmt.Errorf("invalid service type: %s", serviceType)
	}

	response := FrontendWSMessage{
		Type: "service_control_response",
		Data: map[string]interface{}{
			"serviceType": serviceType,
			"serviceName": serviceName,
			"action":      action,
			"success":     err == nil,
		},
		Timestamp: time.Now(),
	}

	if err != nil {
		response.Error = err.Error()
	}

	frontendConnManager.SendToConnection(connID, response)

	// [Translated] 如果操作成功，则广播最新的服务列表
	if err == nil {
		var updatedServices []Service
		var getErr error
		// [Translated] 从原始消息中获取ServerID，这对于广播是必要的
		serverID, ok := msg.Data.(map[string]interface{})["serverID"].(float64) // Assuming serverID is sent as float64 from JS
		if !ok {
			log.Printf("ServerID not found or invalid in service control message data for connID %s", connID)
			return
		}

		switch serviceType {
		case "supervisor":
			updatedServices, getErr = listSupervisorServices()
		case "systemd":
			updatedServices, getErr = listSystemdServices()
		case "docker":
			updatedServices, getErr = listDockerServices()
		}

		if getErr != nil {
			log.Printf("Failed to get updated service list after control operation for %s %s: %v", serviceType, serviceName, getErr)
		} else {
			broadcastMessage := FrontendWSMessage{
				Type:      "service_list_update", // A new type for service list updates
				ServerID:  int(serverID),         // Convert float64 to int
				Data:      map[string]interface{}{"services": updatedServices, "serviceType": serviceType},
				Timestamp: time.Now(),
			}
			// [Translated] 广播给所有订阅了该ServerID的前端连接
			frontendConnManager.BroadcastToSubscribers(int(serverID), broadcastMessage)
			log.Printf("Broadcasted updated %s service list for ServerID %d after control operation.", serviceType, int(serverID))
		}
	}
}

func handleFrontendPing(connID string) {
	response := FrontendWSMessage{
		Type:      "pong",
		Timestamp: time.Now(),
	}
	frontendConnManager.SendToConnection(connID, response)
}

// [Translated] 实时数据广播
func startRealTimeDataBroadcast() {
	ticker := time.NewTicker(1 * time.Second) // [Translated] 每1秒广播一次以进行实时监控
	defer ticker.Stop()

	log.Println("Started real-time data broadcasting (1 second interval)")

	for range ticker.C {
		broadcastSystemStats()
	}
}

// [Translated] getLatestClientStats从数据库中检索特定客户端的最新统计信息
func getLatestClientStats(serverID int) (*StatusInfo, bool) {
	// [Translated] 首先检查缓存
	serverStatusCache.RLock()
	if cachedStats, ok := serverStatusCache.data[serverID]; ok {
		serverStatusCache.RUnlock()
		return cachedStats, true
	}
	serverStatusCache.RUnlock()

	var serverStatus ServerStatus
	dbMutex.RLock()
	err := db.Where("id = ?", serverID).Order("last_active DESC").First(&serverStatus).Error
	dbMutex.RUnlock()
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Return a default StatusInfo and false for 'found'
			return &StatusInfo{
				MemTotal: 1, // Avoid division by zero in frontend if MemTotal is 0
			}, false
		}
		// log.Printf("Failed to query client stats for server ID %d: %v", serverID, err) // Commented out
		// Return a default StatusInfo and false for 'found' on other errors
		return &StatusInfo{
			MemTotal: 1, // Avoid division by zero in frontend if MemTotal is 0
		}, false
	}
	statusInfo := &StatusInfo{
		CPU:         serverStatus.StatusCPU,
		MemUsed:     serverStatus.StatusMemUsed,
		MemTotal:    serverStatus.StatusMemTotal,
		DiskUsed:    serverStatus.StatusDiskUsed,
		DiskTotal:   serverStatus.StatusDiskTotal,
		NetInSpeed:  serverStatus.StatusNetInSpeed,
		NetOutSpeed: serverStatus.StatusNetOutSpeed,
		Uptime:      serverStatus.StatusUptime,
		Load1:       serverStatus.StatusLoad1,
		Load5:       serverStatus.StatusLoad5,
		Load15:      serverStatus.StatusLoad15,
	}

	// [Translated] 更新缓存
	serverStatusCache.Lock()
	serverStatusCache.data[serverID] = statusInfo
	serverStatusCache.Unlock()

	return statusInfo, true
}

func broadcastSystemStats() {
	for _, s := range serverConfig.Servers { // Iterate over configured servers
		stats, _ := getLatestClientStats(s.ID) // Use s.ID

		// 添加详细的广播日志
		// log.Printf("正在广播服务器ID %d (名称: %s) 的系统统计信息 - CPU: %.2f%%, 已用内存: %d, 入站速度: %d, 出站速度: %d", // 已注释掉
		// 	s.ID, s.Name, stats.CPU, stats.MemUsed, stats.NetInSpeed, stats.NetOutSpeed) // 已注释掉

		message := FrontendWSMessage{
			Type:      "system_stats_broadcast",
			ServerID:  s.ID,
			Data:      stats,
			Timestamp: time.Now(),
		}
		frontendConnManager.BroadcastToSubscribers(s.ID, message)
	}
}

// 获取可执行文件的目录
func getExecutableDir() string {
	execPath, err := os.Executable()
	if err != nil {
		log.Fatalf("Failed to get executable path: %v", err)
	}
	return filepath.Dir(execPath)
}

// 获取相对于可执行文件的文件路径
func getFilePath(filename string) string {
	execDir := getExecutableDir()
	return filepath.Join(execDir, filename)
}

// [Translated] loadServerConfig 从指定文件路径加载服务器配置
func loadServerConfig(filePath string) (*ServerConfig, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read server config file %s: %v", filePath, err)
	}
	var config ServerConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, fmt.Errorf("failed to parse server config file %s: %v", filePath, err)
	}
	return &config, nil
}

// [Translated] loadClientConfig 从指定文件路径加载客户端配置
func loadClientConfig(filePath string) (*ClientConfig, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read client config file %s: %v", filePath, err)
	}
	var config ClientConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, fmt.Errorf("failed to parse client config file %s: %v", filePath, err)
	}
	return &config, nil
}

// [Translated] saveWithRetry 使用重试逻辑保存数据库记录
func saveWithRetry(data interface{}, maxRetries int) error {
	dbMutex.Lock()
	defer dbMutex.Unlock()

	for i := 0; i < maxRetries; i++ {
		if err := db.Save(data).Error; err == nil {
			return nil
		} else if strings.Contains(err.Error(), "database is locked") {
			log.Printf("Database is locked, retrying... (attempt %d/%d)", i+1, maxRetries)
			time.Sleep(time.Millisecond * time.Duration(100*(i+1))) // [Translated] 指数退避
			continue
		} else {
			return err
		}
	}
	return fmt.Errorf("failed to save data after %d retries", maxRetries)
}

// [Translated] createWithRetry 使用重试逻辑创建数据库记录
func createWithRetry(data interface{}, maxRetries int) error {
	dbMutex.Lock()
	defer dbMutex.Unlock()

	for i := 0; i < maxRetries; i++ {
		if err := db.Create(data).Error; err == nil {
			return nil
		} else if strings.Contains(err.Error(), "database is locked") {
			log.Printf("Database is locked, retrying... (attempt %d/%d)", i+1, maxRetries)
			time.Sleep(time.Millisecond * time.Duration(100*(i+1))) // [Translated] 指数退避
			continue
		} else {
			return err
		}
	}
	return fmt.Errorf("failed to create data after %d retries", maxRetries)
}

// [Translated] processServiceUpdates 处理服务数据的删除和保存
func processServiceUpdates(serverID int, supervisorServices, systemdServices, dockerServices []Service) {
	dbMutex.Lock()
	tx := db.Begin()
	if tx.Error != nil {
		dbMutex.Unlock()
		log.Printf("Failed to begin transaction for service updates: %v", tx.Error)
		return
	}

	// [Translated] 删除旧的服务记录以避免重复
	if err := tx.Where("server_id = ?", serverID).Delete(&SupervisorService{}).Error; err != nil {
		tx.Rollback()
		log.Printf("Failed to delete old Supervisor services for ServerID %d: %v", serverID, err)
		return
	}
	if err := tx.Where("server_id = ?", serverID).Delete(&SystemdService{}).Error; err != nil {
		tx.Rollback()
		log.Printf("Failed to delete old Systemd services for ServerID %d: %v", serverID, err)
		return
	}
	if err := tx.Where("server_id = ?", serverID).Delete(&DockerService{}).Error; err != nil {
		tx.Rollback()
		log.Printf("Failed to delete old Docker services for ServerID %d: %v", serverID, err)
		return
	}

	// [Translated] 保存新的Supervisor服务
	for _, s := range supervisorServices {
		supervisorService := SupervisorService{
			ServerID:    serverID,
			Name:        s.Name,
			Status:      s.Status,
			Description: s.Description,
			PID:         s.PID,
			Uptime:      s.Uptime,
			Memory:      s.Memory,
			CPU:         s.CPU,
		}
		if err := tx.Create(&supervisorService).Error; err != nil {
			tx.Rollback()
			log.Printf("Failed to save Supervisor service %s for ServerID %d: %v", s.Name, serverID, err)
			return
		}
	}

	// [Translated] 保存新的Systemd服务
	for _, s := range systemdServices {
		systemdService := SystemdService{
			ServerID:    serverID,
			Name:        s.Name,
			Status:      s.Status,
			Description: s.Description,
			PID:         s.PID,
			Uptime:      s.Uptime,
			Memory:      s.Memory,
			CPU:         s.CPU,
		}
		if err := tx.Create(&systemdService).Error; err != nil {
			tx.Rollback()
			log.Printf("Failed to save Systemd service %s for ServerID %d: %v", s.Name, serverID, err)
			return
		}
	}

	// [Translated] 保存新的Docker服务
	for _, s := range dockerServices {
		dockerService := DockerService{
			ServerID:    serverID,
			Name:        s.Name,
			Status:      s.Status,
			Description: s.Description,
			PID:         s.PID,
			Uptime:      s.Uptime,
			Memory:      s.Memory,
			CPU:         s.CPU,
		}
		if err := tx.Create(&dockerService).Error; err != nil {
			tx.Rollback()
			log.Printf("Failed to save Docker service %s for ServerID %d: %v", s.Name, serverID, err)
			return
		}
	}

	// [Translated] 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Printf("Failed to commit transaction for ServerID %d: %v", serverID, err)
		return
	}
	dbMutex.Unlock()
	log.Printf("Successfully saved service data for ServerID %d", serverID)
}
