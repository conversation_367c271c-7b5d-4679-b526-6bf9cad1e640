{
  "version": "2.0.0",
  "tasks": [
    // ==================== Egg.js 开发任务 ====================
    {
      "label": "Egg.js: 开发模式启动",
      "type": "shell",
      "command": "pnpm",
      "args": ["run", "dev:all"],
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "icon": {
        "id": "rocket",
        "color": "terminal.ansiBlue"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "runOptions": {
        "runOn": "folderOpen"
      },
      "problemMatcher": [
        {
          "pattern": [
            {
              "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error)\\s+(.*)$",
              "file": 1,
              "line": 2,
              "column": 3,
              "severity": 4,
              "message": 5
            }
          ]
        }
      ]
    },
    {
      "label": "Egg.js: 生产模式启动",
      "type": "shell",
      "command": "npm",
      "args": ["start"],
      "group": "build",
      "icon": {
        "id": "flame",
        "color": "terminal.ansiRed"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      }
    },
    {
      "label": "Egg.js: 停止服务",
      "type": "shell",
      "command": "npm",
      "args": ["stop"],
      "group": "build",
      "icon": {
        "id": "circle-slash",
        "color": "terminal.ansiBlack"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      }
    },

    // ==================== 测试任务 ====================
    {
      "label": "运行所有测试",
      "type": "shell",
      "command": "npm",
      "args": ["test"],
      "group": "test",
      "icon": {
        "id": "beaker",
        "color": "terminal.ansiYellow"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      }
    },
    {
      "label": "运行测试覆盖率",
      "type": "shell",
      "command": "npm",
      "args": ["run", "cov"],
      "group": "test",
      "icon": {
        "id": "beaker",
        "color": "terminal.ansiMagenta"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      }
    },

    // ==================== 代码质量检查 ====================
    {
      "label": "ESLint: 检查代码",
      "type": "shell",
      "command": "npx",
      "args": ["eslint", ".", "--ext", ".js,.ts"],
      "group": "build",
      "icon": {
        "id": "check",
        "color": "terminal.ansiGreen"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": ["$eslint-stylish"]
    },
    {
      "label": "ESLint: 自动修复",
      "type": "shell",
      "command": "npx",
      "args": ["eslint", ".", "--ext", ".js,.ts", "--fix"],
      "group": "build",
      "icon": {
        "id": "wand",
        "color": "terminal.ansiGreen"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      }
    },
    {
      "label": "Prettier: 格式化代码",
      "type": "shell",
      "command": "npx",
      "args": ["prettier", "--write", "**/*.{js,ts,json,md}"],
      "group": "build",
      "icon": {
        "id": "wand",
        "color": "terminal.ansiBlue"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      }
    },

    // ==================== 依赖管理 ====================
    {
      "label": "安装依赖",
      "type": "shell",
      "command": "npm",
      "args": ["install"],
      "group": "build",
      "icon": {
        "id": "package",
        "color": "terminal.ansiBlue"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      }
    },
    {
      "label": "更新依赖",
      "type": "shell",
      "command": "npm",
      "args": ["update"],
      "group": "build",
      "icon": {
        "id": "package",
        "color": "terminal.ansiYellow"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      }
    },
    {
      "label": "检查过时依赖",
      "type": "shell",
      "command": "npm",
      "args": ["outdated"],
      "group": "build",
      "icon": {
        "id": "alert",
        "color": "terminal.ansiRed"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      }
    },

    // ==================== 数据库任务 ====================
    {
      "label": "数据库迁移",
      "type": "shell",
      "command": "npx",
      "args": ["sequelize", "db:migrate"],
      "group": "build",
      "icon": {
        "id": "database",
        "color": "terminal.ansiBlue"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      }
    },
    {
      "label": "数据库种子",
      "type": "shell",
      "command": "npx",
      "args": ["sequelize", "db:seed:all"],
      "group": "build",
      "icon": {
        "id": "database",
        "color": "terminal.ansiGreen"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      }
    },

    // ==================== 清理任务 ====================
    {
      "label": "清理日志文件",
      "type": "shell",
      "command": "rm",
      "args": ["-rf", "logs/*"],
      "group": "build",
      "icon": {
        "id": "trash",
        "color": "terminal.ansiRed"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "windows": {
        "command": "powershell",
        "args": ["-Command", "Remove-Item -Path logs/* -Recurse -Force"]
      }
    },
    {
      "label": "清理 node_modules",
      "type": "shell",
      "command": "rm",
      "args": ["-rf", "node_modules"],
      "group": "build",
      "icon": {
        "id": "trash",
        "color": "terminal.ansiYellow"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "windows": {
        "command": "powershell",
        "args": ["-Command", "Remove-Item -Path node_modules -Recurse -Force"]
      }
    },

    // ==================== Git 任务 ====================
    {
      "label": "Git: 提交所有更改",
      "type": "shell",
      "command": "git",
      "args": ["add", ".", "&&", "git", "commit", "-m", "Auto commit from VS Code"],
      "group": "build",
      "icon": {
        "id": "cloud-upload",
        "color": "terminal.ansiBlue"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      }
    },
    {
      "label": "Git: 推送到远程",
      "type": "shell",
      "command": "git",
      "args": ["push"],
      "group": "build",
      "icon": {
        "id": "cloud-upload",
        "color": "terminal.ansiGreen"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}"
      }
    }
  ]
}
