#!/usr/bin/env pwsh
# 🧹 完全清理并重新安装 pnpm 依赖

Write-Host "🧹 开始清理 pnpm 依赖..." -ForegroundColor Cyan

# 1. 删除 node_modules
if (Test-Path "node_modules") {
    Write-Host "📁 删除 node_modules..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "node_modules"
    Write-Host "✅ node_modules 已删除" -ForegroundColor Green
} else {
    Write-Host "ℹ️  node_modules 不存在" -ForegroundColor Gray
}

# 2. 清理 pnpm store
Write-Host "🗑️  清理 pnpm store..." -ForegroundColor Yellow
pnpm store prune --force
Write-Host "✅ pnpm store 已清理" -ForegroundColor Green

# 3. 显示当前 pnpm 版本
Write-Host "🔍 当前 pnpm 版本:" -ForegroundColor Blue
pnpm --version

# 4. 重新安装依赖
Write-Host "📦 重新安装依赖..." -ForegroundColor Cyan
pnpm install

Write-Host "🎉 清理并重新安装完成!" -ForegroundColor Green
