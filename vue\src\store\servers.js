import { defineStore } from 'pinia';
import { ref, computed, getCurrentInstance, onMounted, onUnmounted } from 'vue';
import { formatUptime } from '../utils/formatters';
import { APP_CONFIG } from '../config/appConfig';
import { useWebSocket } from '../composables/useWebSocket';
import { useNotificationStore } from './notification'; // Import useNotificationStore

export const useServersStore = defineStore('servers', () => {
  const notificationStore = useNotificationStore(); // Instantiate notification store

  // 状态
  const servers = ref([]);
  const loading = ref(false);
  const error = ref(null); // Keep for internal tracking if needed, but errors will be notified
  const lastUpdate = ref(null);

  // WebSocket 相关状态
  const wsConnected = ref(false);
  const wsReconnectAttempts = ref(0);

  // 计算属性
  const onlineServers = computed(() =>
    servers.value.filter(server => server.status === 'online')
  );

  const offlineServers = computed(() =>
    servers.value.filter(server => server.status === 'offline')
  );

  const warningServers = computed(() =>
    servers.value.filter(server => server.status === 'warning')
  );

  const totalServers = computed(() => servers.value.length);

  const averageCpu = computed(() => {
    if (servers.value.length === 0) return 0;
    const totalCpu = servers.value.reduce(
      (sum, server) => sum + (server.cpu || 0),
      0
    );
    return totalCpu / servers.value.length;
  });

  const averageMemory = computed(() => {
    if (servers.value.length === 0) return 0;
    const totalMemory = servers.value.reduce(
      (sum, server) => sum + (server.memory || 0),
      0
    );
    return totalMemory / servers.value.length;
  });

  // Function to handle system_stats_broadcast from WebSocket
  const handleSystemStatsBroadcast = message => {
    const serverId = message.ServerID;
    const stats = message.Data; // This is a StatusInfo object from Go backend
    const timestamp = message.Timestamp; // This is a time.Time string

    const server = servers.value.find(s => s.id === serverId);
    if (!server) return;

    server.cpu = stats.CPU || 0;
    server.NetInSpeed = stats.NetInSpeed || 0;
    server.NetOutSpeed = stats.NetOutSpeed || 0;
    server.NetInTransfer = stats.NetInTransfer || 0;
    server.NetOutTransfer = stats.NetOutTransfer || 0;
    server.uptime = formatUptime(stats.Uptime || 0);
    server.lastActive = Math.floor(new Date(timestamp).getTime() / 1000); // Use timestamp from message

    // Recalculate memory percentage
    server.memory =
      stats.MemTotal > 0 ? (stats.MemUsed / stats.MemTotal) * 100 : 0;

    // Recalculate status based on new data
    const now = Math.floor(Date.now() / 1000);
    const timeDiff = now - server.lastActive;

    if (timeDiff > APP_CONFIG.WEBSOCKET_OFFLINE_THRESHOLD_SECONDS) {
      server.status = 'offline';
    } else if (
      server.cpu > APP_CONFIG.CPU_WARNING_THRESHOLD ||
      server.memory > APP_CONFIG.MEMORY_WARNING_THRESHOLD
    ) {
      server.status = 'warning';
    } else {
      server.status = 'online';
    }
  };

  // Initialize WebSocket connection
  const {
    connect: wsConnect,
    disconnect: wsDisconnect,
    sendMessage: wsSendMessage,
    isConnected: wsIsConnected,
  } = useWebSocket(
    `ws://${window.location.host}/ws/frontend`, // Adjust URL as needed
    {
      messageHandlers: {
        system_stats_broadcast: handleSystemStatsBroadcast,
        // Add other handlers as needed for different message types
      },
      onOpen: () => {
        wsConnected.value = true;
        console.log('Frontend WS Connected');
      },
      onClose: () => {
        wsConnected.value = false;
        console.log('Frontend WS Disconnected');
      },
      onError: err => {
        console.error('Frontend WS Error:', err);
        error.value = err.message; // For internal tracking
        notificationStore.showNotification(
          `WebSocket连接错误: ${err.message}`,
          'error'
        );
      },
      onReconnectFail: () => {
        console.error('Frontend WS Reconnection failed.');
      },
    }
  );

  // 初始化服务器数据（12个占位符）
  const initializeServers = () => {
    servers.value = [];
    for (let i = 1; i <= 12; i++) {
      servers.value.push({
        id: i,
        name: `服务器 ${i}`,
        ip: 'N/A',
        hostname: 'N/A',
        os: 'N/A',
        status: 'offline',
        cpu: 0,
        memory: 0,
        NetInSpeed: 0,
        NetOutSpeed: 0,
        NetInTransfer: 0,
        NetOutTransfer: 0,
        uptime: '--',
        lastActive: 0,
        isPlaceholder: true,
      });
    }
    // Connect WebSocket when initializing servers
    wsConnect();
  };

  // 获取服务器数据
  const fetchServers = async () => {
    const { proxy } = getCurrentInstance();
    loading.value = true;
    error.value = null;

    try {
      const response = await proxy.$axios.get('/api/servers', {
        withCredentials: true,
      });

      // Axios automatically throws for non-2xx, so no need for response.ok check here

      const apiServers = response.data;

      // 重置所有服务器为离线状态
      servers.value.forEach(server => {
        server.status = 'offline';
        server.cpu = 0;
        server.memory = 0;
        server.NetInSpeed = 0;
        server.NetOutSpeed = 0;
        server.NetInTransfer = 0;
        server.NetOutTransfer = 0;
        server.uptime = '--';
        server.lastActive = 0;
        server.isPlaceholder = true;
      });

      // 更新从API获取的服务器数据
      apiServers.forEach(apiServer => {
        const targetServer = servers.value.find(s => s.id === apiServer.id);

        if (targetServer) {
          const lastActive = apiServer.last_active || 0;
          const now = Math.floor(Date.now() / 1000);
          const timeDiff = now - lastActive;
          let status = 'offline';

          if (timeDiff < APP_CONFIG.WEBSOCKET_OFFLINE_THRESHOLD_SECONDS) {
            if (
              apiServer.status.CPU > APP_CONFIG.CPU_WARNING_THRESHOLD ||
              (apiServer.host.MemTotal > 0 &&
                (apiServer.status.MemUsed / apiServer.host.MemTotal) * 100 >
                  APP_CONFIG.MEMORY_WARNING_THRESHOLD)
            ) {
              status = 'warning';
            } else {
              status = 'online';
            }
          }

          // 更新服务器信息
          Object.assign(targetServer, {
            name: apiServer.name || `服务器 ${apiServer.id}`,
            ip: apiServer.ipv4 || apiServer.valid_ip || 'N/A',
            hostname: apiServer.name || 'N/A',
            os: `${apiServer.host.Platform || 'Unknown'} ${apiServer.host.PlatformVersion || 'OS'}`,
            status,
            cpu: apiServer.status.CPU || 0,
            memory:
              apiServer.host.MemTotal > 0
                ? (apiServer.status.MemUsed / apiServer.host.MemTotal) * 100
                : 0,
            uptime: formatUptime(apiServer.status.Uptime || 0),
            lastActive,
            NetInSpeed: apiServer.status.NetInSpeed || 0,
            NetOutSpeed: apiServer.status.NetOutSpeed || 0,
            NetInTransfer: apiServer.status.NetInTransfer || 0,
            NetOutTransfer: apiServer.status.NetOutTransfer || 0,
            isPlaceholder: false,
          });
        }
      });

      lastUpdate.value = new Date();
      return true;
    } catch (err) {
      console.error('Failed to fetch servers:', err);
      error.value = err.message; // For internal tracking
      notificationStore.showNotification(
        `获取服务器列表失败: ${err.message}`,
        'error'
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 更新单个服务器的实时数据（WebSocket）
  const updateServerStats = (serverId, stats) => {
    const server = servers.value.find(s => s.id === serverId);
    if (!server) return;

    server.cpu = stats.CPU || 0;
    server.memory =
      stats.MemTotal > 0 ? (stats.MemUsed / stats.MemTotal) * 100 : 0;
    server.NetInSpeed = stats.NetInSpeed || 0;
    server.NetOutSpeed = stats.NetOutSpeed || 0;
    server.NetInTransfer = stats.NetInTransfer || 0;
    server.NetOutTransfer = stats.NetOutTransfer || 0;
    server.uptime = formatUptime(stats.Uptime || 0);
    server.lastActive = Math.floor(Date.now() / 1000);

    // 更新状态
    const now = Math.floor(Date.now() / 1000);
    const timeDiff = now - server.lastActive;

    if (timeDiff > APP_CONFIG.WEBSOCKET_OFFLINE_THRESHOLD_SECONDS) {
      server.status = 'offline';
    } else if (
      server.cpu > APP_CONFIG.CPU_WARNING_THRESHOLD ||
      server.memory > APP_CONFIG.MEMORY_WARNING_THRESHOLD
    ) {
      server.status = 'warning';
    } else {
      server.status = 'online';
    }
  };

  // 获取指定服务器
  const getServerById = id => {
    return servers.value.find(server => server.id === parseInt(id));
  };

  // 搜索服务器
  const searchServers = (query, statusFilter = 'all') => {
    const searchTerm = query.toLowerCase();

    return servers.value.filter(server => {
      const matchesSearch =
        server.name.toLowerCase().includes(searchTerm) ||
        server.hostname.toLowerCase().includes(searchTerm) ||
        server.ip.includes(searchTerm);

      const matchesStatus =
        statusFilter === 'all' || server.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  };

  // 重置状态
  const reset = () => {
    servers.value = [];
    loading.value = false;
    error.value = null;
    lastUpdate.value = null;
    wsConnected.value = false;
    wsReconnectAttempts.value = 0;
  };

  // 辅助函数 (已迁移到 formatters.js)

  // Automatically connect/disconnect WebSocket when the store is used/destroyed
  onMounted(() => {
    // wsConnect(); // Moved to initializeServers for explicit control
  });

  onUnmounted(() => {
    wsDisconnect();
  });

  return {
    // 状态
    servers,
    loading,
    error,
    lastUpdate,
    wsConnected,
    wsReconnectAttempts,

    // 计算属性
    onlineServers,
    offlineServers,
    warningServers,
    totalServers,
    averageCpu,
    averageMemory,

    // 方法
    initializeServers,
    fetchServers,
    // updateServerStats is now handled internally by handleSystemStatsBroadcast
    getServerById,
    searchServers,
    reset,

    // Expose WebSocket control for advanced scenarios if needed
    wsConnect,
    wsDisconnect,
    wsSendMessage,
    wsIsConnected,
  };
});
