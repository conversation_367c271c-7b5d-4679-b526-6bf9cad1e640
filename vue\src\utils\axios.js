import axios from 'axios';
import { ElMessage } from 'element-plus';
import router from '../router'; // Assuming router is in ../router/index.js

const instance = axios.create({
  baseURL: '/', // Your API base URL
  timeout: 10000, // Request timeout
  withCredentials: true, // Send cookies with requests
});

// Response interceptor
instance.interceptors.response.use(
  response => {
    // Any status code that lie within the range of 2xx cause this function to trigger
    // Do something with response data
    return response;
  },
  error => {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    // Do something with response error
    if (error.response) {
      const { status, data } = error.response;
      let errorMessage = data.message || '请求失败，请稍后再试。';

      switch (status) {
        case 400:
          errorMessage = data.message || '请求参数错误。';
          break;
        case 401:
          errorMessage = data.message || '认证失败，请重新登录。';
          // Redirect to login page
          ElMessage.error(errorMessage);
          router.push('/login');
          break;
        case 403:
          errorMessage = data.message || '权限不足，无法访问。';
          break;
        case 404:
          errorMessage = data.message || '请求资源不存在。';
          break;
        case 500:
          errorMessage = data.message || '服务器内部错误。';
          break;
        default:
          errorMessage = `HTTP 错误: ${status} - ${errorMessage}`;
      }
      ElMessage.error(errorMessage);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('Network Error:', error.message);
      ElMessage.error('网络错误，请检查您的网络连接。');
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error:', error.message);
      ElMessage.error('发生未知错误: ' + error.message);
    }
    return Promise.reject(error);
  }
);

export default instance;
