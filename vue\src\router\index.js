import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/LoginView.vue'),
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../views/DashboardView.vue'),
  },
  {
    path: '/services/:mid?',
    name: 'Services',
    component: () => import('../views/ServicesView.vue'),
    props: true,
  },
  {
    path: '/',
    redirect: '/dashboard', // Default route
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
