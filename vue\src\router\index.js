import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/LoginView.vue'),
    meta: { requiresAuth: false },
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../views/DashboardView.vue'),
    meta: { requiresAuth: true },
  },
  {
    path: '/services/:mid?',
    name: 'Services',
    component: () => import('../views/ServicesView.vue'),
    props: true,
    meta: { requiresAuth: true },
  },
  {
    path: '/',
    redirect: '/dashboard', // Default route
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/dashboard',
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 路由守卫 - 检查认证状态
router.beforeEach(async (to, from, next) => {
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);

  if (requiresAuth) {
    try {
      // 检查认证状态 - 通过调用后端API验证cookie
      const response = await fetch('/api/servers', {
        method: 'GET',
        credentials: 'include', // 包含httpOnly cookie
      });

      if (response.ok) {
        // 认证成功，允许访问
        next();
      } else {
        // 认证失败，重定向到登录页
        next('/login');
      }
    } catch (error) {
      console.error('Authentication check failed:', error);
      next('/login');
    }
  } else {
    // 不需要认证的页面，直接允许访问
    next();
  }
});

export default router;
