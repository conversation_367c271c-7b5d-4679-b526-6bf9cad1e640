import { defineStore } from 'pinia';
import { ref, computed, getCurrentInstance } from 'vue';
import { formatUptime } from '../utils/formatters';
import { useNotificationStore } from './notification'; // Import useNotificationStore

export const useServicesStore = defineStore('services', () => {
  const notificationStore = useNotificationStore(); // Instantiate notification store

  // 状态
  const services = ref({
    supervisor: [],
    systemd: [],
    docker: [],
  });

  const systemStats = ref({
    cpu: 0,
    memory: 0,
    memUsed: 0,
    memTotal: 0,
    disk: 0,
    diskUsed: 0,
    diskTotal: 0,
    netInTransfer: 0,
    netOutTransfer: 0,
  });

  const loading = ref({
    supervisor: false,
    systemd: false,
    docker: false,
    stats: false,
  });

  const error = ref({
    supervisor: null,
    systemd: null,
    docker: null,
    stats: null,
  });

  const currentServerId = ref(null);
  const lastUpdate = ref({
    supervisor: null,
    systemd: null,
    docker: null,
    stats: null,
  });

  // 计算属性
  const totalServices = computed(() => {
    return Object.values(services.value).reduce((total, serviceList) => {
      return total + serviceList.length;
    }, 0);
  });

  const runningServices = computed(() => {
    return Object.values(services.value).reduce((total, serviceList) => {
      return (
        total +
        serviceList.filter(
          service => service.status === 'running' || service.status === 'active'
        ).length
      );
    }, 0);
  });

  const stoppedServices = computed(() => {
    return Object.values(services.value).reduce((total, serviceList) => {
      return (
        total +
        serviceList.filter(
          service =>
            service.status === 'stopped' || service.status === 'inactive'
        ).length
      );
    }, 0);
  });

  const failedServices = computed(() => {
    return Object.values(services.value).reduce((total, serviceList) => {
      return (
        total +
        serviceList.filter(
          service => service.status === 'failed' || service.status === 'error'
        ).length
      );
    }, 0);
  });

  // 获取服务列表
  const fetchServices = async (serviceType, serverId) => {
    const { proxy } = getCurrentInstance();
    if (!serverId) {
      services.value[serviceType] = [];
      return;
    }

    loading.value[serviceType] = true;
    error.value[serviceType] = null;

    try {
      const response = await proxy.$axios.get(
        `/api/services/list?serverId=${serverId}&serviceType=${serviceType}`,
        { withCredentials: true }
      );

      // Axios automatically throws for non-2xx, so no need for response.ok check here

      // Format uptime for each service
      services.value[serviceType] = response.data.map(service => ({
        ...service,
        uptime: formatUptime(service.uptime || 0),
      }));
      lastUpdate.value[serviceType] = new Date();

      return true;
    } catch (err) {
      console.error(`Error fetching ${serviceType} services:`, err);
      const errorMessage = err.response?.data?.error || err.message;
      error.value[serviceType] = errorMessage; // For internal tracking
      notificationStore.showNotification(
        `获取${serviceType}服务列表失败: ${errorMessage}`,
        'error'
      );
      services.value[serviceType] = [];
      return false;
    } finally {
      loading.value[serviceType] = false;
    }
  };

  // 获取系统统计信息
  const fetchSystemStats = async serverId => {
    if (!serverId) {
      systemStats.value = {
        cpu: 0,
        memory: 0,
        memUsed: 0,
        memTotal: 0,
        disk: 0,
        diskUsed: 0,
        diskTotal: 0,
        netInTransfer: 0,
        netOutTransfer: 0,
      };
      return;
    }

    loading.value.stats = true;
    error.value.stats = null;

    try {
      const response = await proxy.$axios.get(
        `/api/system/stats?serverId=${serverId}`,
        { withCredentials: true }
      );

      // Axios automatically parses JSON and throws for non-2xx
      const stats = response.data;

      systemStats.value = {
        cpu: stats.CPU || 0,
        memUsed: stats.MemUsed || 0,
        memTotal: stats.MemTotal || 0,
        memory: stats.MemTotal > 0 ? (stats.MemUsed / stats.MemTotal) * 100 : 0,
        diskUsed: stats.DiskUsed || 0,
        diskTotal: stats.DiskTotal || 0,
        disk:
          stats.DiskTotal > 0 ? (stats.DiskUsed / stats.DiskTotal) * 100 : 0,
        netInTransfer: stats.NetInTransfer || 0,
        netOutTransfer: stats.NetOutTransfer || 0,
      };

      lastUpdate.value.stats = new Date();
      return true;
    } catch (err) {
      console.error('Error fetching system stats:', err);
      const errorMessage = err.response?.data?.error || err.message;
      error.value.stats = errorMessage; // For internal tracking
      notificationStore.showNotification(
        `获取系统统计信息失败: ${errorMessage}`,
        'error'
      );
      return false;
    } finally {
      loading.value.stats = false;
    }
  };

  // 服务操作
  const serviceAction = async (action, serviceName, serviceType, serverId) => {
    if (!serverId) {
      notificationStore.showNotification('请先选择一个服务器', 'warning');
      return { success: false, message: '请先选择一个服务器' };
    }

    try {
      const response = await proxy.$axios.post(
        `/api/services/${serviceType}/${action}`,
        {
          serverId: parseInt(serverId),
          serviceName,
          serviceType,
        },
        {
          withCredentials: true,
        }
      );

      // Axios automatically throws for non-2xx

      // 操作成功后刷新服务列表
      await fetchServices(serviceType, serverId);
      notificationStore.showNotification(
        `${serviceName} 服务 ${action} 成功`,
        'success'
      );
      return { success: true, message: `${serviceName} 服务 ${action} 成功` };
    } catch (err) {
      console.error(`Error ${action} service:`, err);
      const errorMessage = err.response?.data?.error || err.message;
      notificationStore.showNotification(
        `${serviceName} 服务 ${action} 失败: ${errorMessage}`,
        'error'
      );
      return { success: false, message: errorMessage };
    }
  };

  // 启动服务
  const startService = async (serviceName, serviceType, serverId) => {
    return await serviceAction('start', serviceName, serviceType, serverId);
  };

  // 停止服务
  const stopService = async (serviceName, serviceType, serverId) => {
    return await serviceAction('stop', serviceName, serviceType, serverId);
  };

  // 重启服务
  const restartService = async (serviceName, serviceType, serverId) => {
    return await serviceAction('restart', serviceName, serviceType, serverId);
  };

  // 获取服务日志
  const getServiceLogs = async (serviceName, serviceType, serverId) => {
    if (!serverId) {
      notificationStore.showNotification('请先选择一个服务器', 'warning');
      return { success: false, logs: '', message: '请先选择一个服务器' };
    }

    try {
      const response = await proxy.$axios.get(
        `/api/services/logs?serverId=${serverId}&serviceName=${serviceName}&serviceType=${serviceType}`,
        { withCredentials: true }
      );

      // Axios automatically throws for non-2xx

      return { success: true, logs: response.data, message: '日志获取成功' };
    } catch (err) {
      console.error('Error fetching service logs:', err);
      const errorMessage = err.response?.data?.error || err.message;
      notificationStore.showNotification(
        `获取 ${serviceName} 服务日志失败: ${errorMessage}`,
        'error'
      );
      return { success: false, logs: '', message: errorMessage };
    }
  };

  // 搜索服务
  const searchServices = (serviceType, query, statusFilter = 'all') => {
    const serviceList = services.value[serviceType] || [];
    const searchTerm = query.toLowerCase();

    return serviceList.filter(service => {
      const matchesSearch = service.name.toLowerCase().includes(searchTerm);

      const matchesStatus =
        statusFilter === 'all' ||
        (statusFilter === 'running' &&
          (service.status === 'running' || service.status === 'active')) ||
        (statusFilter === 'stopped' &&
          (service.status === 'stopped' || service.status === 'inactive')) ||
        (statusFilter === 'failed' &&
          (service.status === 'failed' || service.status === 'error'));

      return matchesSearch && matchesStatus;
    });
  };

  // 更新系统统计信息（WebSocket）
  const updateSystemStats = stats => {
    systemStats.value = {
      cpu: stats.CPU || 0,
      memUsed: stats.MemUsed || 0,
      memTotal: stats.MemTotal || 0,
      memory: stats.MemTotal > 0 ? (stats.MemUsed / stats.MemTotal) * 100 : 0,
      diskUsed: stats.DiskUsed || 0,
      diskTotal: stats.DiskTotal || 0,
      disk: stats.DiskTotal > 0 ? (stats.DiskUsed / stats.DiskTotal) * 100 : 0,
      netInTransfer: stats.NetInTransfer || 0,
      netOutTransfer: stats.NetOutTransfer || 0,
    };
  };

  // 设置当前服务器ID
  const setCurrentServerId = serverId => {
    currentServerId.value = serverId;
  };

  // 重置状态
  const reset = () => {
    services.value = {
      supervisor: [],
      systemd: [],
      docker: [],
    };
    systemStats.value = {
      cpu: 0,
      memory: 0,
      memUsed: 0,
      memTotal: 0,
      disk: 0,
      diskUsed: 0,
      diskTotal: 0,
      netInTransfer: 0,
      netOutTransfer: 0,
    };
    loading.value = {
      supervisor: false,
      systemd: false,
      docker: false,
      stats: false,
    };
    error.value = {
      supervisor: null,
      systemd: null,
      docker: null,
      stats: null,
    };
    currentServerId.value = null;
    lastUpdate.value = {
      supervisor: null,
      systemd: null,
      docker: null,
      stats: null,
    };
  };

  return {
    // 状态
    services,
    systemStats,
    loading,
    error,
    currentServerId,
    lastUpdate,

    // 计算属性
    totalServices,
    runningServices,
    stoppedServices,
    failedServices,

    // 方法
    fetchServices,
    fetchSystemStats,
    startService,
    stopService,
    restartService,
    getServiceLogs,
    searchServices,
    updateSystemStats,
    setCurrentServerId,
    reset,
  };
});
