// tailwind.config.js (适用于 Vite)
export default {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  corePlugins: {
    preflight: true, // 启用预设样式，确保基础样式正常
  },
  theme: {
    extend: {
      // 自定义动画
      animation: {
        'spin-custom': 'spin 0.5s linear',
      },
      // 自定义颜色
      colors: {
        'status-online': '#10b981',
        'status-offline': '#ef4444',
        'status-warning': '#f59e0b',
      },
      // 自定义工具类
      utilities: {
        'img-inline': {
          display: 'inline',
        },
      },
    },
  },
  plugins: [],
};
