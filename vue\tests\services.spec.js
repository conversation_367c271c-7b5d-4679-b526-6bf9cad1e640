import { test, expect } from '@playwright/test';

test.describe('服务管理页面测试', () => {
  test.beforeEach(async ({ page }) => {
    // 模拟已登录状态
    await page.route('/api/servers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 1,
            name: '测试服务器1',
            ipv4: '*************'
          }
        ])
      });
    });

    // 模拟系统统计API
    await page.route('/api/system/stats*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          CPU: 45.5,
          MemUsed: 4294967296,
          MemTotal: 8589934592,
          DiskUsed: 107374182400,
          DiskTotal: 214748364800,
          NetInTransfer: 1073741824,
          NetOutTransfer: 536870912
        })
      });
    });

    // 模拟服务列表API
    await page.route('/api/services/list*', async route => {
      const url = new URL(route.request().url());
      const serviceType = url.searchParams.get('serviceType');
      
      let services = [];
      if (serviceType === 'supervisor') {
        services = [
          { name: 'nginx', status: 'running', description: 'Nginx Web Server' },
          { name: 'redis', status: 'stopped', description: 'Redis Cache Server' }
        ];
      } else if (serviceType === 'systemd') {
        services = [
          { name: 'apache2', status: 'active', description: 'Apache HTTP Server' },
          { name: 'mysql', status: 'inactive', description: 'MySQL Database' }
        ];
      } else if (serviceType === 'docker') {
        services = [
          { name: 'webapp', status: 'running', description: 'Web Application Container' },
          { name: 'database', status: 'stopped', description: 'Database Container' }
        ];
      }
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(services)
      });
    });

    await page.goto('/services/1');
  });

  test('页面加载和基本元素', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/服务器监控系统/);
    
    // 检查主要组件
    await expect(page.locator('h1')).toContainText('服务管理');
    await expect(page.locator('.server-selector')).toBeVisible();
    await expect(page.locator('.system-stats')).toBeVisible();
    await expect(page.locator('.service-tabs')).toBeVisible();
  });

  test('服务器选择器', async ({ page }) => {
    // 检查服务器选择器
    const serverSelector = page.locator('.server-selector select');
    await expect(serverSelector).toBeVisible();
    
    // 检查默认选中的服务器
    await expect(serverSelector).toHaveValue('1');
    
    // 检查服务器选项
    await expect(serverSelector.locator('option')).toContainText('测试服务器1');
  });

  test('系统统计信息显示', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 检查统计卡片
    const statsCards = page.locator('.stat-card');
    await expect(statsCards).toHaveCount(4);
    
    // 检查CPU使用率
    await expect(page.locator('.stat-card').first()).toContainText('45.5%');
    
    // 检查内存使用率
    await expect(page.locator('.stat-card').nth(1)).toContainText('50.0%');
    
    // 检查磁盘使用率
    await expect(page.locator('.stat-card').nth(2)).toContainText('50.0%');
  });

  test('服务标签页切换', async ({ page }) => {
    // 等待页面加载
    await page.waitForTimeout(1000);
    
    // 检查默认标签页（Supervisor）
    await expect(page.locator('.el-tabs__item.is-active')).toContainText('Supervisor');
    
    // 切换到Systemd标签页
    await page.click('.el-tabs__item:has-text("Systemd")');
    await expect(page.locator('.el-tabs__item.is-active')).toContainText('Systemd');
    
    // 切换到Docker标签页
    await page.click('.el-tabs__item:has-text("Docker")');
    await expect(page.locator('.el-tabs__item.is-active')).toContainText('Docker');
  });

  test('Supervisor服务列表', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 检查服务列表
    const serviceItems = page.locator('.service-item');
    await expect(serviceItems).toHaveCount(2);
    
    // 检查第一个服务
    const firstService = serviceItems.first();
    await expect(firstService).toContainText('nginx');
    await expect(firstService).toContainText('running');
    await expect(firstService).toContainText('Nginx Web Server');
    
    // 检查第二个服务
    const secondService = serviceItems.nth(1);
    await expect(secondService).toContainText('redis');
    await expect(secondService).toContainText('stopped');
  });

  test('服务操作按钮', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    const firstService = page.locator('.service-item').first();
    
    // 检查操作按钮
    await expect(firstService.locator('button:has-text("停止")')).toBeVisible();
    await expect(firstService.locator('button:has-text("重启")')).toBeVisible();
    await expect(firstService.locator('button:has-text("日志")')).toBeVisible();
    
    // 检查停止的服务显示启动按钮
    const stoppedService = page.locator('.service-item').nth(1);
    await expect(stoppedService.locator('button:has-text("启动")')).toBeVisible();
  });

  test('服务启动操作', async ({ page }) => {
    // 模拟服务启动API
    await page.route('/api/services/supervisor/start', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });
    
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 点击启动按钮
    const stoppedService = page.locator('.service-item').nth(1);
    await stoppedService.locator('button:has-text("启动")').click();
    
    // 检查成功消息
    await expect(page.locator('.el-message--success')).toBeVisible();
  });

  test('服务停止操作', async ({ page }) => {
    // 模拟服务停止API
    await page.route('/api/services/supervisor/stop', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });
    
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 点击停止按钮
    const runningService = page.locator('.service-item').first();
    await runningService.locator('button:has-text("停止")').click();
    
    // 确认对话框
    await page.click('button:has-text("确定")');
    
    // 检查成功消息
    await expect(page.locator('.el-message--success')).toBeVisible();
  });

  test('服务重启操作', async ({ page }) => {
    // 模拟服务重启API
    await page.route('/api/services/supervisor/restart', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });
    
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 点击重启按钮
    const runningService = page.locator('.service-item').first();
    await runningService.locator('button:has-text("重启")').click();
    
    // 确认对话框
    await page.click('button:has-text("确定")');
    
    // 检查成功消息
    await expect(page.locator('.el-message--success')).toBeVisible();
  });

  test('查看服务日志', async ({ page }) => {
    // 模拟日志API
    await page.route('/api/services/logs*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'text/plain',
        body: '2024-01-01 12:00:00 [INFO] Service started\n2024-01-01 12:01:00 [INFO] Processing request\n2024-01-01 12:02:00 [ERROR] Connection failed'
      });
    });
    
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 点击日志按钮
    const firstService = page.locator('.service-item').first();
    await firstService.locator('button:has-text("日志")').click();
    
    // 检查日志对话框
    await expect(page.locator('.el-dialog')).toBeVisible();
    await expect(page.locator('.el-dialog__title')).toContainText('服务日志');
    await expect(page.locator('.log-content')).toContainText('Service started');
  });

  test('服务搜索功能', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 搜索nginx服务
    await page.fill('input[placeholder="搜索服务..."]', 'nginx');
    
    // 检查搜索结果
    const visibleServices = page.locator('.service-item:visible');
    await expect(visibleServices).toHaveCount(1);
    await expect(visibleServices.first()).toContainText('nginx');
    
    // 清空搜索
    await page.fill('input[placeholder="搜索服务..."]', '');
    await expect(page.locator('.service-item')).toHaveCount(2);
  });

  test('服务状态过滤', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 过滤运行中的服务
    await page.selectOption('.status-filter select', 'running');
    
    const runningServices = page.locator('.service-item:visible');
    await expect(runningServices).toHaveCount(1);
    await expect(runningServices.first()).toContainText('nginx');
    
    // 过滤停止的服务
    await page.selectOption('.status-filter select', 'stopped');
    
    const stoppedServices = page.locator('.service-item:visible');
    await expect(stoppedServices).toHaveCount(1);
    await expect(stoppedServices.first()).toContainText('redis');
    
    // 显示所有服务
    await page.selectOption('.status-filter select', 'all');
    await expect(page.locator('.service-item')).toHaveCount(2);
  });

  test('Systemd服务管理', async ({ page }) => {
    // 切换到Systemd标签页
    await page.click('.el-tabs__item:has-text("Systemd")');
    
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 检查Systemd服务列表
    const serviceItems = page.locator('.service-item');
    await expect(serviceItems).toHaveCount(2);
    
    // 检查服务状态
    await expect(serviceItems.first()).toContainText('apache2');
    await expect(serviceItems.first()).toContainText('active');
    
    await expect(serviceItems.nth(1)).toContainText('mysql');
    await expect(serviceItems.nth(1)).toContainText('inactive');
  });

  test('Docker容器管理', async ({ page }) => {
    // 切换到Docker标签页
    await page.click('.el-tabs__item:has-text("Docker")');
    
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 检查Docker容器列表
    const serviceItems = page.locator('.service-item');
    await expect(serviceItems).toHaveCount(2);
    
    // 检查容器状态
    await expect(serviceItems.first()).toContainText('webapp');
    await expect(serviceItems.first()).toContainText('running');
    
    await expect(serviceItems.nth(1)).toContainText('database');
    await expect(serviceItems.nth(1)).toContainText('stopped');
  });

  test('错误处理', async ({ page }) => {
    // 模拟API错误
    await page.route('/api/services/supervisor/start', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' })
      });
    });
    
    // 等待数据加载
    await page.waitForTimeout(1000);
    
    // 尝试启动服务
    const stoppedService = page.locator('.service-item').nth(1);
    await stoppedService.locator('button:has-text("启动")').click();
    
    // 检查错误消息
    await expect(page.locator('.el-message--error')).toBeVisible();
  });

  test('响应式设计', async ({ page }) => {
    // 测试桌面视图
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('.service-tabs')).toBeVisible();
    
    // 测试平板视图
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('.service-tabs')).toBeVisible();
    
    // 测试移动视图
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('.service-tabs')).toBeVisible();
  });
});
