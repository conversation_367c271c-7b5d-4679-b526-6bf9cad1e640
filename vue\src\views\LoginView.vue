<template>
  <div class="login-container flex items-center justify-center">
    <div class="login-form rounded-lg p-8 w-full max-w-md mx-4">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-white mb-2">服务器监控系统</h1>
        <p class="text-white/80">请登录以继续</p>
      </div>

      <form @submit.prevent="handleLogin" class="space-y-6">
        <div>
          <label
            for="username"
            class="block text-white text-sm font-medium mb-2"
            >用户名</label
          >
          <input
            type="text"
            id="username"
            v-model="username"
            required
            class="input-field w-full px-4 py-3 rounded-lg transition-all duration-200"
            placeholder="请输入用户名"
          />
        </div>

        <div>
          <label
            for="password"
            class="block text-white text-sm font-medium mb-2"
            >密码</label
          >
          <input
            type="password"
            id="password"
            v-model="password"
            required
            class="input-field w-full px-4 py-3 rounded-lg transition-all duration-200"
            placeholder="请输入密码"
          />
        </div>

        <button
          type="submit"
          :disabled="loading"
          class="w-full bg-white text-purple-600 font-semibold py-3 px-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
        >
          {{ loading ? '登录中...' : '登录' }}
        </button>
      </form>

      <div
        v-if="errorMessage"
        class="mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-200 text-sm"
      >
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script setup>
import axios from 'axios';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

const username = ref('');
const password = ref('');
const loading = ref(false);
const errorMessage = ref('');
const router = useRouter();

const handleLogin = async () => {
  loading.value = true;
  errorMessage.value = '';

  try {
    const response = await axios.post(
      '/api/login',
      {
        username: username.value,
        password: password.value,
      },
      {
        withCredentials: true, // Important for httpOnly cookie
      }
    );

    // 检查响应状态
    if (response.status === 200) {
      router.push('/dashboard');
    } else {
      errorMessage.value = response.data.error || '登录失败';
    }
  } catch (error) {
    console.error('Login error:', error);
    errorMessage.value = '网络错误，请稍后重试';
  } finally {
    loading.value = false;
  }
};

// Check if already logged in on component mount
onMounted(async () => {
  try {
    const response = await axios.get('/api/servers', {
      withCredentials: true, // Include cookies in the request
    });
    if (response.status === 200) {
      router.push('/dashboard');
    }
  } catch (error) {
    // Not authenticated, stay on login page
    console.log('User not authenticated or API error:', error.message);
  }
});
</script>

<style scoped>
.login-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}
.login-form {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.input-field {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}
.input-field::placeholder {
  color: rgba(255, 255, 255, 0.7);
}
.input-field:focus {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  outline: none;
}
</style>
